<template>
  <div class="roi-management-section">
    <h4>ROI区域管理</h4>
    
    <div class="roi-content">
      <div v-if="roiList.length === 0" class="no-roi-message">
        未创建ROI区域，请点击工具栏中的"绘制"按钮开始创建
      </div>
      
      <div v-else class="roi-list">
        <div
          v-for="roi in roiList"
          :key="roi.roi_id"
          class="roi-item-compact"
          :class="{ 'highlighted': roi.roi_id === highlightedROIId }"
        >
          <!-- 第一行：名称、基本信息和主要操作 -->
          <div class="roi-line-1">
            <div class="roi-name-info">
              <span class="roi-name" :style="{ color: roi.color }">
                {{ roi.name || '未命名区域' }}
              </span>
              <span class="roi-basic-info">
                ID: {{ roi.roi_id?.substring(0, 8) || 'N/A' }} | {{ formatRoiType(roi.roi_type) }} | {{ roi.points?.length || 0 }}点
                <span v-if="roi.attribute"> | {{ roi.attribute }}</span>
              </span>
            </div>
            <div class="roi-main-actions">
              <!-- ROI启停控制按钮 -->
              <button
                class="btn-toggle-compact"
                @click="toggleROIActive(roi.roi_id)"
                :class="{ 'active': getROIActiveStatus(roi.roi_id), 'inactive': !getROIActiveStatus(roi.roi_id) }"
                :title="getROIActiveStatus(roi.roi_id) ? '停止检测' : '启动检测'"
              >
                {{ getROIActiveStatus(roi.roi_id) ? '停止' : '启动' }}
              </button>
              <button
                class="btn-highlight-compact"
                @click="toggleHighlight(roi.roi_id)"
                :class="{ 'active': roi.roi_id === highlightedROIId }"
                :title="roi.roi_id === highlightedROIId ? '取消高亮' : '高亮'"
              >
                {{ roi.roi_id === highlightedROIId ? '取消' : '高亮' }}
              </button>
              <button class="btn-edit-compact" @click="editRoi(roi)" title="编辑">编辑</button>
              <button class="btn-delete-compact" @click="deleteRoi(roi.roi_id)" title="删除">删除</button>
            </div>
          </div>
          
          <!-- 第二行：算法配置（仅在启用算法选择时显示） -->
          <div class="roi-line-2" v-if="enableAlgorithmSelection">
            <div class="roi-algorithm-compact">
              <span class="algorithm-label">算法:</span>
              <div class="algorithm-content" v-if="roi.attribute === 'pailiao'">
                <label class="algorithm-radio">
                  <input 
                    type="radio" 
                    :name="`algorithm-${roi.roi_id}`"
                    value="background_subtraction"
                    :checked="getRoiAlgorithm(roi.roi_id) === 'background_subtraction'"
                    @change="setRoiAlgorithm(roi.roi_id, 'background_subtraction')"
                  /> 
                  背景减除
                </label>
                <label class="algorithm-radio">
                  <input 
                    type="radio" 
                    :name="`algorithm-${roi.roi_id}`"
                    value="frame_difference"
                    :checked="getRoiAlgorithm(roi.roi_id) === 'frame_difference'"
                    @change="setRoiAlgorithm(roi.roi_id, 'frame_difference')"
                  /> 
                  帧差法
                </label>
              </div>
              <span class="algorithm-content" v-else-if="roi.attribute === 'yazhu'">
                方向检测
              </span>
              <span class="algorithm-content" v-else>
                请先设置属性
              </span>
            </div>
            <button
              class="btn-config-compact"
              @click="() => configureRoiAlgorithmFinal(roi.roi_id)"
              :disabled="!roi.attribute"
              title="配置参数"
            >
              配置
            </button>
          </div>
        </div>
      </div>
      
      <div class="roi-actions-global">
        <button 
          class="btn-clear-attribute" 
          @click="$emit('clear-attribute')"
          :disabled="roiList.length === 0"
        >
          清除所有属性
        </button>
      </div>
    </div>
    
    <!-- ROI编辑对话框 -->
    <div v-if="showEditDialog" class="roi-edit-dialog">
      <div class="dialog-content">
        <h5>编辑ROI</h5>
        
        <div class="form-group">
          <label>名称</label>
          <input type="text" v-model="editingRoi.name" />
        </div>
        
        <div class="form-group">
          <label>颜色</label>
          <input type="color" v-model="editingRoi.color" />
        </div>

        <!-- 属性不允许编辑，只显示 -->
        <div class="form-group">
          <label>属性</label>
          <div class="readonly-field">{{ getAttributeDisplayName(editingRoi.attribute) }}</div>
        </div>
        
        <div class="dialog-actions">
          <button class="btn-cancel" @click="cancelEdit">取消</button>
          <button class="btn-save" @click="saveEdit">保存</button>
        </div>
      </div>
    </div>
    
    <!-- ROI算法配置对话框 -->
    <div v-if="showAlgorithmDialog" class="roi-algorithm-dialog">
      <div class="dialog-content">
        <h5>ROI算法配置</h5>
        
        <div class="algorithm-type">
          当前ROI: {{ currentRoiName }}
        </div>
        
        <!-- 方向检测算法参数 -->
        <div v-if="currentRoiAlgorithm === 'direction'" class="algorithm-params">
          <div class="params-section">
            <h6>前置背景检测参数</h6>
            
            <div class="form-group">
              <label>
                <input 
                  type="checkbox" 
                  v-model="algorithmParams.enabled"
                /> 
                启用检测
              </label>
            </div>
            
            <div class="form-group">
              <label>背景更新率</label>
              <div class="slider-container">
                <input 
                  type="range" 
                  min="1" 
                  max="50" 
                  v-model.number="algorithmParams.backgroundUpdateRateScale" 
                  class="slider"
                />
                <span>{{ (algorithmParams.backgroundUpdateRate).toFixed(3) }}</span>
              </div>
            </div>
            
            <div class="form-group">
              <label>运动检测阈值</label>
              <div class="slider-container">
                <input 
                  type="range" 
                  min="10" 
                  max="200" 
                  v-model.number="algorithmParams.motionThreshold" 
                  class="slider"
                />
                <span>{{ algorithmParams.motionThreshold }}</span>
              </div>
            </div>
            
            <div class="form-group">
              <label>最小检测面积</label>
              <div class="slider-container">
                <input 
                  type="range" 
                  min="10" 
                  max="1000" 
                  v-model.number="algorithmParams.minArea" 
                  class="slider"
                />
                <span>{{ algorithmParams.minArea }}px</span>
              </div>
            </div>
          </div>
          
          <div class="params-section">
            <h6>后置方向检测参数</h6>
            
            <div class="form-group">
              <label>连续检测帧数阈值</label>
              <div class="slider-container">
                <input 
                  type="range" 
                  min="1" 
                  max="10" 
                  v-model.number="algorithmParams.consecutiveDetectionThreshold" 
                  class="slider"
                />
                <span>{{ algorithmParams.consecutiveDetectionThreshold }}</span>
              </div>
            </div>
            
            <div class="form-group">
              <label>最小位移量</label>
              <div class="slider-container">
                <input 
                  type="range" 
                  min="1" 
                  max="10" 
                  v-model.number="algorithmParams.minDisplacement" 
                  class="slider"
                />
                <span>{{ algorithmParams.minDisplacement }}px</span>
              </div>
            </div>
            
            <div class="form-group">
              <label>抖动容忍度</label>
              <div class="slider-container">
                <input 
                  type="range" 
                  min="0" 
                  max="10" 
                  v-model.number="algorithmParams.maxPatience" 
                  class="slider"
                />
                <span>{{ algorithmParams.maxPatience }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 背景减除法参数 -->
        <div v-else-if="currentRoiAlgorithm === 'background_subtraction'" class="algorithm-params">
          <div class="form-group">
            <label>最小检测面积</label>
            <div class="slider-container">
              <input 
                type="range" 
                min="50" 
                max="500" 
                v-model.number="algorithmParams.minArea" 
                class="slider"
              />
              <span>{{ algorithmParams.minArea }}px</span>
            </div>
          </div>
          
          <div class="form-group">
            <label>检测阈值</label>
            <div class="slider-container">
              <input 
                type="range" 
                min="10" 
                max="100" 
                v-model.number="algorithmParams.detectionThreshold" 
                class="slider"
              />
              <span>{{ algorithmParams.detectionThreshold }}</span>
            </div>
          </div>
          
          <div class="form-group">
            <label>学习速率</label>
            <div class="slider-container">
              <input 
                type="range" 
                min="1" 
                max="20" 
                v-model.number="algorithmParams.learningRateScale" 
                class="slider"
              />
              <span>{{ (algorithmParams.learningRateScale * 0.001).toFixed(4) }}</span>
            </div>
          </div>
          
          <div class="form-group">
            <label>
              <input 
                type="checkbox" 
                v-model="algorithmParams.detectShadows"
              /> 
              检测阴影
            </label>
          </div>
        </div>
        
        <!-- 帧差法参数 -->
        <div v-else-if="currentRoiAlgorithm === 'frame_difference'" class="algorithm-params">
          <div class="form-group">
            <label>最小检测面积</label>
            <div class="slider-container">
              <input 
                type="range" 
                min="50" 
                max="500" 
                v-model.number="algorithmParams.minArea" 
                class="slider"
              />
              <span>{{ algorithmParams.minArea }}px</span>
            </div>
          </div>
          
          <div class="form-group">
            <label>差异阈值</label>
            <div class="slider-container">
              <input 
                type="range" 
                min="10" 
                max="100" 
                v-model.number="algorithmParams.threshold" 
                class="slider"
              />
              <span>{{ algorithmParams.threshold }}</span>
            </div>
          </div>
          
          <div class="form-group">
            <label>帧间隔</label>
            <div class="slider-container">
              <input 
                type="range" 
                min="1" 
                max="5" 
                v-model.number="algorithmParams.frameInterval" 
                class="slider"
              />
              <span>{{ algorithmParams.frameInterval }}</span>
            </div>
          </div>
        </div>
        
        <div class="dialog-actions">
          <button class="btn-cancel" @click="cancelAlgorithmConfig">取消</button>
          <button class="btn-save" @click="saveAlgorithmConfig">保存</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, watch, inject } from 'vue'
import { ElMessage } from 'element-plus'

// 定义组件属性
const props = defineProps({
  // ROI列表
  roiList: {
    type: Array as any,
    default: () => []
  },
  // 选中的属性
  selectedAttribute: {
    type: String,
    default: ''
  },
  // 是否启用绘制模式
  isDrawingEnabled: {
    type: Boolean,
    default: false
  },
  // 高亮的ROI ID
  highlightedROIId: {
    type: String,
    default: ''
  },
  // 是否启用算法选择
  enableAlgorithmSelection: {
    type: Boolean,
    default: true
  },
  // ROI算法配置
  roiAlgorithms: {
    type: Object as any,
    default: () => ({})
  }
})

// 定义事件
const emit = defineEmits(['edit', 'delete', 'highlight', 'clear-attribute', 'algorithm-change', 'algorithm-config'])

// 注入ROI活跃状态管理函数
const getROIActiveStatus = inject<(roiId: string) => boolean>('getROIActiveStatus')
const setROIActiveStatus = inject<(roiId: string, isActive: boolean) => void>('setROIActiveStatus')

// ROI启停控制函数
const toggleROIActive = (roiId: string) => {
  if (!getROIActiveStatus || !setROIActiveStatus) {
    ElMessage.error('ROI活跃状态管理功能未初始化')
    return
  }
  
  const currentStatus = getROIActiveStatus(roiId)
  const newStatus = !currentStatus
  
  setROIActiveStatus(roiId, newStatus)
  
  ElMessage.success(`ROI ${roiId} 已${newStatus ? '启动' : '停止'}检测`)
}

// 编辑对话框状态
const showEditDialog = ref(false)
const editingRoi = ref<any>({})
const editingRoiId = ref('')

// 算法配置对话框状态
const showAlgorithmDialog = ref(false)
const currentRoiId = ref('')
const currentRoiAlgorithm = ref('background_subtraction')
const algorithmParams = ref<any>({
  minArea: 100,
  detectionThreshold: 40,
  learningRateScale: 5,
  detectShadows: true,
  threshold: 30,
  frameInterval: 2
})

// 当前ROI名称
const currentRoiName = computed(() => {
  const roi = props.roiList.find((r: any) => r.roi_id === currentRoiId.value)
  return roi ? roi.name || `ROI ${currentRoiId.value}` : '未知ROI'
})

// 格式化ROI类型
const formatRoiType = (type: string) => {
  const typeMap: Record<string, string> = {
    'polygon': '多边形',
    'rectangle': '矩形',
    'circle': '圆形',
    'line': '线段'
  }
  
  return typeMap[type] || type
}

// 格式化算法类型
const formatAlgorithmType = (type: string) => {
  const typeMap: Record<string, string> = {
    'background_subtraction': '背景减除法',
    'frame_difference': '帧差法'
  }

  return typeMap[type] || type
}

// 获取属性显示名称
const getAttributeDisplayName = (attribute: string) => {
  const attributeMap: Record<string, string> = {
    'yazhu': '压铸机',
    'pailiao': '排料口'
  }
  return attributeMap[attribute] || '未设置'
}

// 获取ROI的算法类型
const getRoiAlgorithm = (roiId: string) => {
  // 首先从roiAlgorithms中获取
  if (props.roiAlgorithms && props.roiAlgorithms[roiId]) {
    return props.roiAlgorithms[roiId].type || 'background_subtraction'
  }

  // 然后从ROI的params中获取
  const roi = props.roiList.find((r: any) => r.roi_id === roiId)
  if (roi && roi.params) {
    // 新格式：从motion_detection.algorithm获取
    if (roi.params.motion_detection && roi.params.motion_detection.algorithm) {
      return roi.params.motion_detection.algorithm
    }
    // 旧格式：从运动检测.algorithm获取
    if (roi.params.运动检测 && roi.params.运动检测.algorithm) {
      const algorithm = roi.params.运动检测.algorithm
      if (algorithm === '背景减除法') {
        return 'background_subtraction'
      } else if (algorithm === '帧差法') {
        return 'frame_difference'
      }
    }
  }

  return 'frame_difference' // 默认使用帧差法
}

// 配置ROI算法（最终版本）
const configureRoiAlgorithmFinal = async (roiId: string) => {
  try {
    await configureRoiAlgorithmSimple(roiId)
  } catch (error) {
    ElMessage.error(`配置失败: ${error}`)
  }
}

// 简化版算法配置函数
const configureRoiAlgorithmSimple = async (roiId: string) => {
  try {
    currentRoiId.value = roiId
    const roi = props.roiList.find((r: any) => r.roi_id === roiId)

    if (!roi) {
      ElMessage.error('未找到ROI')
      return
    }

    if (!roi.attribute) {
      ElMessage.warning('请先设置ROI属性')
      return
    }

    // 从数据库获取最新配置
    let dbParams = {}

    try {
      const response = await fetch(`/api/roi-config/${roiId}`)
      if (response.ok) {
        const result = await response.json()
        const rawDbParams = result.data?.algorithm_params || {}

        // 🔥 关键修复：正确解析嵌套的参数结构
        if (roi.attribute === 'yazhu') {
          // 压铸机：合并"前置背景检测"和"后置方向检测"参数
          const frontParams = rawDbParams['前置背景检测'] || {}
          const backParams = rawDbParams['后置方向检测'] || {}
          dbParams = {
            ...frontParams,
            ...backParams,
            type: rawDbParams.type
          }
        } else {
          // 排料口：从"运动检测"字段提取参数
          dbParams = rawDbParams['运动检测'] || rawDbParams
        }
      } else {
        ElMessage.warning('从数据库获取ROI配置失败，使用本地数据')
      }
    } catch (error) {
      ElMessage.error(`数据库API调用失败: ${error}`)
    }

    // 参数合并
    const roiAlgorithmParams = props.roiAlgorithms[roiId] || {}
    const roiParams = roi.params || {}
    const currentParams = { ...roiParams, ...roiAlgorithmParams, ...dbParams }

    // 根据ROI属性设置算法类型和参数
    if (roi.attribute === 'yazhu') {
      currentRoiAlgorithm.value = 'direction'

      // 处理新格式和旧格式的参数
      let motionDetection = currentParams.motion_detection || currentParams.前置背景检测 || {}
      let directionDetection = currentParams.direction_detection || currentParams.后置方向检测 || {}

      algorithmParams.value = {
        enabled: motionDetection.enabled !== undefined ? motionDetection.enabled : (currentParams.enabled !== undefined ? currentParams.enabled : true),
        backgroundUpdateRateScale: motionDetection.backgroundUpdateRate ? motionDetection.backgroundUpdateRate * 100 : (currentParams.backgroundUpdateRate ? currentParams.backgroundUpdateRate * 100 : 1),
        get backgroundUpdateRate() {
          return this.backgroundUpdateRateScale * 0.01
        },
        motionThreshold: motionDetection.motionThreshold !== undefined ? motionDetection.motionThreshold : (currentParams.motionThreshold !== undefined ? currentParams.motionThreshold : 50),
        minArea: motionDetection.minArea !== undefined ? motionDetection.minArea : (currentParams.minArea !== undefined ? currentParams.minArea : 500),
        consecutiveDetectionThreshold: directionDetection.consecutiveDetectionThreshold !== undefined ? directionDetection.consecutiveDetectionThreshold : (currentParams.consecutiveDetectionThreshold !== undefined ? currentParams.consecutiveDetectionThreshold : 3),
        minDisplacement: directionDetection.minDisplacement !== undefined ? directionDetection.minDisplacement : (currentParams.minDisplacement !== undefined ? currentParams.minDisplacement : 2),
        maxPatience: directionDetection.maxPatience !== undefined ? directionDetection.maxPatience : (currentParams.maxPatience !== undefined ? currentParams.maxPatience : 3)
      }
    } else {
      // 排料口
      currentRoiAlgorithm.value = getRoiAlgorithm(roiId)

      // 处理新格式和旧格式的参数
      let motionDetection = currentParams.motion_detection || currentParams.运动检测 || {}

      if (currentRoiAlgorithm.value === 'background_subtraction') {
        algorithmParams.value = {
          minArea: motionDetection.minArea !== undefined ? motionDetection.minArea : (currentParams.minArea !== undefined ? currentParams.minArea : 100),
          detectionThreshold: motionDetection.detectionThreshold !== undefined ? motionDetection.detectionThreshold : (currentParams.detectionThreshold !== undefined ? currentParams.detectionThreshold : 40),
          learningRateScale: motionDetection.learningRate ? motionDetection.learningRate * 1000 : (currentParams.learningRate ? currentParams.learningRate * 1000 : 5),
          detectShadows: motionDetection.shadowRemoval !== undefined ? motionDetection.shadowRemoval > 0 : (currentParams.shadowsThreshold !== undefined ? currentParams.shadowsThreshold > 0 : false)
        }
      } else {
        algorithmParams.value = {
          minArea: motionDetection.minArea !== undefined ? motionDetection.minArea : (currentParams.minArea !== undefined ? currentParams.minArea : 100),
          threshold: motionDetection.threshold !== undefined ? motionDetection.threshold : (currentParams.threshold !== undefined ? currentParams.threshold : 30),
          frameInterval: motionDetection.frameInterval !== undefined ? motionDetection.frameInterval : (currentParams.frameInterval !== undefined ? currentParams.frameInterval : 2)
        }
      }
    }

    // 打开对话框
    showAlgorithmDialog.value = true

  } catch (error) {
    ElMessage.error(`配置失败: ${error}`)
  }
}

// 设置ROI的算法类型
const setRoiAlgorithm = (roiId: string, algorithm: string) => {
  emit('algorithm-change', { roiId, algorithm })
}

// 打开编辑对话框
const editRoi = async (roi: any) => {
  // 从数据库加载最新的ROI信息
  try {
    const response = await fetch(`/api/roi-config/load-by-video-source`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        video_source_id: roi.video_source_id || 'default_video_source',
        video_source_path: roi.video_source_path || '/default/video/path'
      })
    })

    if (response.ok) {
      const configs = await response.json()
      const roiConfig = configs.find((config: any) => config.roi_id === roi.roi_id)

      if (roiConfig) {
        // 使用数据库中的最新数据
        editingRoi.value = {
          roi_id: roiConfig.roi_id, // 🔥 使用roi_id字段
          name: roiConfig.name,
          color: roiConfig.color || roi.color || '#ff0000', // 优先使用数据库中的颜色
          attribute: roiConfig.attribute
        }
      } else {
        // 如果数据库中没有找到，使用当前ROI数据
        editingRoi.value = { ...roi }
      }
    } else {
      // 如果请求失败，使用当前ROI数据
      editingRoi.value = { ...roi }
    }
  } catch (error) {
    console.error('加载ROI配置失败:', error)
    // 如果出错，使用当前ROI数据
    editingRoi.value = { ...roi }
  }

  editingRoiId.value = roi.roi_id
  showEditDialog.value = true
}

// 保存编辑
const saveEdit = () => {
  emit('edit', { roi_id: editingRoiId.value, ...editingRoi.value })
  showEditDialog.value = false
}

// 取消编辑
const cancelEdit = () => {
  showEditDialog.value = false
}

// 删除ROI
const deleteRoi = (roiId: string) => {
  emit('delete', roiId)
}

// 切换高亮
const toggleHighlight = (roiId: string) => {
  emit('highlight', roiId)
}

// 打开算法配置对话框（重定向到修复版本）
const configureRoiAlgorithm = async (roiId: string) => {
  await configureRoiAlgorithmSimple(roiId)
}

// 保存算法配置
const saveAlgorithmConfig = () => {
  let params: any = {}
  const roi = props.roiList.find((r: any) => r.roi_id === currentRoiId.value)

  if (!roi) {
    showAlgorithmDialog.value = false
    return
  }

  if (roi.attribute === 'yazhu') {
    // 方向检测算法参数 - 使用新的英文格式
    params = {
      type: 'direction',
      motion_detection: {
        enabled: algorithmParams.value.enabled,
        backgroundUpdateRate: algorithmParams.value.backgroundUpdateRateScale * 0.01,
        motionThreshold: algorithmParams.value.motionThreshold,
        minArea: algorithmParams.value.minArea
      },
      direction_detection: {
        consecutiveDetectionThreshold: algorithmParams.value.consecutiveDetectionThreshold,
        minDisplacement: algorithmParams.value.minDisplacement,
        maxPatience: algorithmParams.value.maxPatience
      }
    }
  } else {
    // pailiao类型ROI - 使用新的英文格式，保持完整参数结构
    params = {
      type: 'motion',  // 保持统一的类型标识
      motion_detection: {
        algorithm: currentRoiAlgorithm.value === 'background_subtraction' ? 'background_subtraction' : 'frame_difference',
        // 保持完整的参数结构，包含所有算法参数
        learningRate: currentRoiAlgorithm.value === 'background_subtraction'
          ? algorithmParams.value.learningRateScale * 0.001
          : 0.01,  // 帧差法保持默认值
        detectionThreshold: currentRoiAlgorithm.value === 'background_subtraction'
          ? algorithmParams.value.detectionThreshold
          : 50,  // 帧差法保持默认值
        shadowRemoval: currentRoiAlgorithm.value === 'background_subtraction'
          ? (algorithmParams.value.detectShadows ? 0.5 : 0)
          : 0.5,  // 帧差法保持默认值
        threshold: currentRoiAlgorithm.value === 'frame_difference'
          ? algorithmParams.value.threshold
          : 30,  // 背景减除法保持默认值
        frameInterval: currentRoiAlgorithm.value === 'frame_difference'
          ? algorithmParams.value.frameInterval
          : 2,  // 背景减除法保持默认值
        minArea: algorithmParams.value.minArea  // 两种算法都使用
      }
    }
  }

  emit('algorithm-config', { roiId: currentRoiId.value, params })
  showAlgorithmDialog.value = false
}

// 取消算法配置
const cancelAlgorithmConfig = () => {
  showAlgorithmDialog.value = false
}

// 组件调试信息
onMounted(() => {
  console.log('🔧 ROIManagement组件已挂载')
  console.log('🔧 ROI列表长度:', props.roiList.length)
  console.log('🔧 ROI列表数据:', props.roiList)
})

watch(() => props.roiList, (newList) => {
  console.log('🔧 ROI列表更新，新长度:', newList.length)
  console.log('🔧 ROI列表数据:', newList)
}, { deep: true })
</script>

<style scoped>
.roi-management-section {
  margin-top: 15px;
  padding: 10px;
  background-color: var(--bg-color-soft);
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.roi-management-section h4 {
  margin: 0 0 10px 0;
  color: var(--text-color);
  font-size: 16px;
}

.roi-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.no-roi-message {
  padding: 20px;
  text-align: center;
  color: var(--text-color-mute);
  background-color: var(--bg-color);
  border-radius: 4px;
  border: 1px dashed var(--border-color);
}

.roi-list {
  max-height: 400px;
  overflow-y: auto;
}

/* 紧凑型ROI项目样式 */
.roi-item-compact {
  padding: 8px;
  background-color: var(--bg-color);
  border-radius: 4px;
  border: 1px solid var(--border-color);
  margin-bottom: 8px;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.roi-item-compact.highlighted {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
}

/* 第一行：名称和主要操作 */
.roi-line-1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 24px;
}

.roi-name-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.roi-name {
  font-weight: bold;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.roi-basic-info {
  font-size: 12px;
  color: var(--text-color-soft);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.roi-main-actions {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

/* 第二行：算法配置 */
.roi-line-2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 24px;
  font-size: 12px;
}

.roi-algorithm-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.algorithm-label {
  font-weight: bold;
  color: var(--text-color-soft);
  white-space: nowrap;
}

.algorithm-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-color-soft);
}

.algorithm-radio {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 11px;
  white-space: nowrap;
}

.algorithm-radio input[type="radio"] {
  margin: 0;
  width: 12px;
  height: 12px;
}

.roi-actions-global {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}

button {
  padding: 4px 8px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 紧凑型按钮样式 */
.btn-highlight-compact {
  background-color: var(--text-color-mute);
  color: white;
  padding: 3px 6px;
  font-size: 11px;
  min-width: 36px;
}

.btn-highlight-compact.active {
  background-color: var(--primary-color);
}

.btn-edit-compact {
  background-color: var(--success-color);
  color: white;
  padding: 3px 6px;
  font-size: 11px;
  min-width: 32px;
}

.btn-delete-compact {
  background-color: var(--danger-color);
  color: white;
  padding: 3px 6px;
  font-size: 11px;
  min-width: 32px;
}

.btn-config-compact {
  background-color: var(--text-color-mute);
  color: white;
  padding: 3px 6px;
  font-size: 11px;
  min-width: 32px;
  flex-shrink: 0;
}

/* 保留原有按钮样式用于对话框 */
.btn-highlight {
  background-color: var(--text-color-mute);
  color: white;
}

.btn-highlight.active {
  background-color: var(--primary-color);
}

.btn-edit {
  background-color: var(--success-color);
  color: white;
}

.btn-delete {
  background-color: var(--danger-color);
  color: white;
}

.btn-config {
  background-color: var(--text-color-mute);
  color: white;
  margin-top: 5px;
}

.btn-clear-attribute {
  background-color: var(--text-color-mute);
  color: white;
}

.roi-edit-dialog,
.roi-algorithm-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog-content {
  width: 400px;
  background-color: var(--bg-color);
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.dialog-content h5 {
  margin: 0 0 15px 0;
  color: var(--text-color);
  font-size: 16px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: var(--text-color-soft);
}

.form-group input[type="text"],
.form-group input[type="color"],
.form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-color);
  color: var(--text-color);
}

.readonly-field {
  width: 100%;
  padding: 8px;
  background-color: var(--bg-color-mute);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  color: var(--text-color-soft);
  font-size: 14px;
}

.algorithm-type {
  margin-bottom: 15px;
  color: var(--primary-color);
  font-weight: bold;
}

.algorithm-params {
  margin-bottom: 15px;
}

.params-section {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px dashed var(--border-color);
}

.params-section h6 {
  margin: 0 0 10px 0;
  color: var(--text-color);
  font-size: 14px;
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.slider {
  flex: 1;
  height: 6px;
}

.slider-container span {
  min-width: 45px;
  text-align: right;
  font-size: 13px;
  color: var(--text-color-soft);
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.btn-cancel {
  background-color: var(--text-color-mute);
  color: white;
  padding: 8px 16px;
}

.btn-save {
  background-color: var(--primary-color);
  color: white;
  padding: 8px 16px;
}
/* ROI启停按钮样式 */
.roi-toggle-btn {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid;
  min-width: 40px;
  text-align: center;
}

.roi-toggle-btn.active {
  background-color: #f56565;
  color: white;
  border-color: #f56565;
}

.roi-toggle-btn.active:hover {
  background-color: #e53e3e;
  border-color: #e53e3e;
}

.roi-toggle-btn.inactive {
  background-color: #48bb78;
  color: white;
  border-color: #48bb78;
}

.roi-toggle-btn.inactive:hover {
  background-color: #38a169;
  border-color: #38a169;
}

/* 暗色模式下的ROI启停按钮样式 */
.dark-theme .roi-toggle-btn.active {
  background-color: #fc8181;
  border-color: #fc8181;
}

.dark-theme .roi-toggle-btn.active:hover {
  background-color: #f56565;
  border-color: #f56565;
}

.dark-theme .roi-toggle-btn.inactive {
  background-color: #68d391;
  border-color: #68d391;
}

.dark-theme .roi-toggle-btn.inactive:hover {
  background-color: #48bb78;
  border-color: #48bb78;
}
</style>