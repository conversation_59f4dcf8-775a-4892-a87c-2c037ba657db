<template>
  <div>
    <!-- 视频预览区域 -->
    <div class="video-preview-section">
      <div
        ref="videoContainer"
        class="video-container"
        :style="videoContainerStyle"
      >
        <div
          id="divPlugin"
          class="video-player"
        ></div>

        <!-- 运动检测叠加层 -->
        <MotionOverlay
          v-if="showMotionDetection && isPreviewActive"
          :video-width="actualVideoWidth || 640"
          :video-height="actualVideoHeight || 360"
          :detection-result="detectionResult"
          :show-status="true"
          class="motion-overlay"
        />

        <!-- ROI绘制结果显示层 -->
        <canvas
          ref="roiDisplayCanvas"
          class="roi-display-canvas"
          :width="actualVideoWidth || 640"
          :height="actualVideoHeight || 360"
        ></canvas>
      </div>
    </div>

    <!-- 控制按钮区域 - 在检测模式下隐藏 -->
    <div class="control-section" v-if="!isDetectionMode">
      <button
        @click="$emit('start-preview')"
        :disabled="isPreviewActive || isConnecting"
        class="control-btn primary"
      >
        {{ isConnecting ? '连接中...' : '开启预览' }}
      </button>
      <button
        @click="$emit('stop-preview')"
        :disabled="!isPreviewActive"
        class="control-btn danger"
      >
        停止预览
      </button>
      <!-- 隐藏运动检测按钮，但保留事件处理以保持兼容性 -->
      <!-- <button
        @click="$emit('toggle-motion')"
        :disabled="!isPreviewActive"
        :class="['control-btn', showMotionDetection ? 'active' : 'secondary']"
      >
        {{ showMotionDetection ? '关闭运动检测' : '开启运动检测' }}
      </button> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import MotionOverlay from '@/components/MotionOverlay.vue'

// 导入类型定义，与MotionOverlay组件匹配
interface Point {
  x: number
  y: number
}

interface MotionContour {
  points: Point[]
  type?: string        // 轮廓类型：normal, direction_arrow
  color?: string       // 轮廓颜色：green, red
  direction?: string   // 方向信息：MOVING_UP, MOVING_DOWN, STATIONARY
}

interface Direction {
  angle: number
  magnitude: number
  vector: Point
}

interface ROIViolation {
  roi_id: string
  roi_name: string
  violation_type: string
}

interface DetectionResult {
  motion_detected: boolean
  contours: MotionContour[]
  direction?: Direction
  roi_violations: ROIViolation[]
}

// 使用Vue Props定义
const props = defineProps({
  isPreviewActive: {
    type: Boolean,
    default: false
  },
  isConnecting: {
    type: Boolean,
    default: false
  },
  showMotionDetection: {
    type: Boolean,
    default: false
  },
  detectionResult: {
    type: Object,
    default: null
  },
  videoContainerStyle: {
    type: Object,
    default: () => ({})
  },
  roiList: {
    type: Array,
    default: () => []
  },
  highlightedROIId: {
    type: String,
    default: null
  },
  isDetectionMode: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'start-preview',
  'stop-preview',
  'toggle-motion',
  'roi-drawer-ready',
  'video-size-changed' // 新增视频尺寸变化事件
])

// DOM引用
const videoContainer = ref<HTMLElement | null>(null)
const roiDisplayCanvas = ref<HTMLCanvasElement | null>(null)

// 视频实际尺寸
const actualVideoWidth = ref<number | null>(null)
const actualVideoHeight = ref<number | null>(null)

/**
 * 更新视频尺寸
 * 从WebSDK获取实际视频尺寸或接收外部传入的尺寸
 */
function updateVideoSize(width?: number, height?: number): { width: number, height: number } {
  console.log('[VIDEO] 更新视频尺寸', width, height)
  
  // 如果传入了明确的尺寸，直接使用
  if (width && height) {
    actualVideoWidth.value = width
    actualVideoHeight.value = height
    
    // 通知父组件视频尺寸已变化
    emit('video-size-changed', { width, height })
    return { width, height }
  }
  
  // 尝试从WebSDK获取视频尺寸
  try {
    const webVideoCtrl = (window as any).WebVideoCtrl
    if (webVideoCtrl && webVideoCtrl.I_GetWindowStatus) {
      // 获取当前窗口状态 (窗口索引为0)
      const status = webVideoCtrl.I_GetWindowStatus(0)
      if (status) {
        console.log('[VIDEO] WebSDK窗口状态:', JSON.stringify(status))
        
        // 检查视频播放状态 (1-预览，2-回放)
        if (status.iPlayStatus === 1 || status.iPlayStatus === 2) {
          // 检查视频元素尺寸
          const videoElement = document.querySelector('#divPlugin video')
          if (videoElement) {
            const videoNaturalWidth = (videoElement as any).videoWidth
            const videoNaturalHeight = (videoElement as any).videoHeight
            
            // 如果能从video元素获取到真实尺寸
            if (videoNaturalWidth > 0 && videoNaturalHeight > 0) {
              console.log('[VIDEO] 视频实际尺寸:', videoNaturalWidth, 'x', videoNaturalHeight)
              actualVideoWidth.value = videoNaturalWidth
              actualVideoHeight.value = videoNaturalHeight
              
              // 通知父组件视频尺寸已变化
              emit('video-size-changed', { 
                width: videoNaturalWidth, 
                height: videoNaturalHeight 
              })
              
              return { width: videoNaturalWidth, height: videoNaturalHeight }
            }
          }
        }
      }
    }
  } catch (error) {
    console.error('[VIDEO] 获取视频尺寸失败:', error)
  }
  
  // 如果无法获取实际尺寸，使用默认尺寸
  const defaultWidth = 640
  const defaultHeight = 360
  
  if (!actualVideoWidth.value || !actualVideoHeight.value) {
    actualVideoWidth.value = defaultWidth
    actualVideoHeight.value = defaultHeight
    console.log('[VIDEO] 使用默认尺寸:', defaultWidth, 'x', defaultHeight)
  }
  
  return { 
    width: actualVideoWidth.value as number, 
    height: actualVideoHeight.value as number 
  }
}

// 初始化ROI绘制器
const initROIDrawer = (): void => {
  if (roiDisplayCanvas.value) {
    // 先尝试获取最新的视频尺寸
    const { width, height } = updateVideoSize()
    
    // 确保canvas尺寸正确
    const canvas = roiDisplayCanvas.value
    canvas.width = width
    canvas.height = height
    
    // 确保画布设置为显示
    canvas.style.display = 'block'
    canvas.style.position = 'absolute'
    canvas.style.top = '0'
    canvas.style.left = '0'
    canvas.style.pointerEvents = 'auto'
    canvas.style.zIndex = '10' // 确保canvas在视频上方
    
    // 检查canvas准备情况
    console.log('ROI画布准备完成:', {
      width: canvas.width,
      height: canvas.height,
      display: canvas.style.display,
      position: canvas.style.position,
      pointerEvents: canvas.style.pointerEvents,
      canvas: canvas instanceof HTMLCanvasElement,
      context: canvas.getContext('2d') !== null
    })
    
    // 通知父组件canvas已准备好
    console.log('ROI画布已准备好，尺寸:', canvas.width, 'x', canvas.height, '通知父组件初始化ROI绘制器')
    emit('roi-drawer-ready', canvas)
  } else {
    console.error('ROI画布元素未找到')
  }
}

// 向父组件暴露DOM引用和方法
defineExpose({
  roiDisplayCanvas,
  actualVideoWidth,
  actualVideoHeight,
  updateVideoSize,
  initROIDrawer
})

// 监听ROI列表变化，更新高亮ROI
watch(() => props.roiList, (newList) => {
  console.log('ROI列表已更新:', newList)
}, { deep: true })

// 监听高亮ROI ID变化
watch(() => props.highlightedROIId, (newId) => {
  console.log('高亮ROI ID已更新:', newId)
})

// 监听视频容器尺寸变化，重新初始化ROI绘制器
watch(() => props.isPreviewActive, (active) => {
  if (active) {
    // 视频开始预览时，延迟一会儿确保视频尺寸已更新，再初始化ROI绘制器
    setTimeout(() => {
      // 更新视频尺寸并初始化ROI绘制器
      const { width, height } = updateVideoSize()
      console.log('[VIDEO] 视频预览启动后获取到的尺寸:', width, 'x', height)
      emit('video-size-changed', { width, height })
      
      // 只有在尺寸发生变化时才重新初始化
      if (width !== 640 || height !== 360) {
        initROIDrawer()
      }
    }, 1000) // 延迟1秒，给视频加载留出时间
    
    // 设置额外的检查点，以确保能捕获到视频尺寸变化
    setTimeout(() => {
      const { width, height } = updateVideoSize()
      console.log('[VIDEO] 视频预览2秒后获取到的尺寸:', width, 'x', height)
      emit('video-size-changed', { width, height })
    }, 2000)
  }
})

onMounted(() => {
  // 初始化时，使用默认尺寸，但不要立即初始化ROI绘制器
  // 等待视频预览开始后再初始化
  setTimeout(() => {
    console.log('[VIDEO] VideoPlayer组件挂载完成，但等待视频预览开始后再初始化ROI')
    
    // 记录上次检测到的尺寸，避免重复通知
    let lastWidth = actualVideoWidth.value
    let lastHeight = actualVideoHeight.value
    
    // 设置定期检查视频尺寸的定时器（仅在预览激活时）
    const checkSizeInterval = setInterval(() => {
      if (props.isPreviewActive) {
        const { width, height } = updateVideoSize()
        
        // 只有当尺寸真正发生变化时才通知父组件
        if (width !== lastWidth || height !== lastHeight) {
          console.log('[VIDEO] 检测到视频尺寸变化:', width, 'x', height)
          // 更新上次检测到的尺寸
          lastWidth = width
          lastHeight = height
          // 通知父组件尺寸变化，但不触发重新初始化
          emit('video-size-changed', { width, height, skipReinitialization: true })
        }
      }
    }, 5000) // 每5秒检查一次
    
    // 组件卸载时清理定时器
    onUnmounted(() => {
      clearInterval(checkSizeInterval)
    })
  }, 500)
})

// 组件卸载时清理资源
onUnmounted(() => {
  console.log('VideoPlayer组件卸载，通知清理ROI资源')
})
</script>