#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于python-snap7的西门子S7 PLC通信示例
GitHub: https://github.com/gijzelaerr/python-snap7
"""

import sys
import struct
import time
from datetime import datetime

try:
    import snap7
    from snap7.util import *
    print(f"✅ snap7库版本: {snap7.__version__}")
except ImportError:
    print("❌ 请安装snap7库: pip install python-snap7")
    print("📖 参考: https://github.com/gijzelaerr/python-snap7")
    sys.exit(1)


class S7Client:
    """S7 PLC客户端封装类"""
    
    def __init__(self):
        self.client = snap7.client.Client()
        self.connected = False
        
    def connect(self, ip, rack=0, slot=1):
        """连接到PLC"""
        try:
            print(f"🔗 连接到 {ip}, Rack:{rack}, Slot:{slot}")
            self.client.connect(ip, rack, slot)
            
            if self.client.get_connected():
                self.connected = True
                print("✅ 连接成功!")
                
                # 获取PLC信息
                try:
                    cpu_info = self.client.get_cpu_info()
                    print(f"📋 CPU型号: {cpu_info.ModuleTypeName}")
                    print(f"📋 CPU版本: {cpu_info.ModuleVersion}")
                    print(f"📋 序列号: {cpu_info.SerialNumber}")
                except Exception as e:
                    print(f"⚠️ 获取CPU信息失败: {e}")
                    
                return True
            else:
                print("❌ 连接失败")
                return False
                
        except Exception as e:
            print(f"❌ 连接异常: {e}")
            return False
            
    def disconnect(self):
        """断开连接"""
        if self.connected:
            self.client.disconnect()
            self.connected = False
            print("🔌 已断开连接")
            
    def read_db(self, db_number, start, size):
        """读取DB数据块"""
        if not self.connected:
            print("❌ 未连接到PLC")
            return None
            
        try:
            print(f"📖 读取 DB{db_number}.{start}, 长度:{size}")
            data = self.client.db_read(db_number, start, size)
            print(f"✅ 读取成功: {data.hex()}")
            return data
        except Exception as e:
            print(f"❌ 读取失败: {e}")
            return None
            
    def write_db(self, db_number, start, data):
        """写入DB数据块"""
        if not self.connected:
            print("❌ 未连接到PLC")
            return False
            
        try:
            print(f"✏️ 写入 DB{db_number}.{start}, 数据:{data.hex()}")
            self.client.db_write(db_number, start, data)
            print("✅ 写入成功")
            return True
        except Exception as e:
            print(f"❌ 写入失败: {e}")
            return False
            
    def read_bool(self, db_number, start, bit):
        """读取布尔值"""
        data = self.read_db(db_number, start, 1)
        if data:
            return get_bool(data, 0, bit)
        return None
        
    def write_bool(self, db_number, start, bit, value):
        """写入布尔值"""
        data = self.read_db(db_number, start, 1)
        if data:
            data = bytearray(data)
            set_bool(data, 0, bit, value)
            return self.write_db(db_number, start, data)
        return False
        
    def read_int(self, db_number, start):
        """读取16位整数"""
        data = self.read_db(db_number, start, 2)
        if data:
            return get_int(data, 0)
        return None
        
    def write_int(self, db_number, start, value):
        """写入16位整数"""
        data = bytearray(2)
        set_int(data, 0, value)
        return self.write_db(db_number, start, data)
        
    def read_dint(self, db_number, start):
        """读取32位整数"""
        data = self.read_db(db_number, start, 4)
        if data:
            return get_dint(data, 0)
        return None
        
    def write_dint(self, db_number, start, value):
        """写入32位整数"""
        data = bytearray(4)
        set_dint(data, 0, value)
        return self.write_db(db_number, start, data)
        
    def read_real(self, db_number, start):
        """读取32位浮点数"""
        data = self.read_db(db_number, start, 4)
        if data:
            return get_real(data, 0)
        return None
        
    def write_real(self, db_number, start, value):
        """写入32位浮点数"""
        data = bytearray(4)
        set_real(data, 0, value)
        return self.write_db(db_number, start, data)
        
    def read_string(self, db_number, start, max_len=254):
        """读取字符串"""
        data = self.read_db(db_number, start, max_len + 2)
        if data:
            return get_string(data, 0)
        return None
        
    def write_string(self, db_number, start, value, max_len=254):
        """写入字符串"""
        data = bytearray(max_len + 2)
        set_string(data, 0, value, max_len)
        return self.write_db(db_number, start, data)


def demo_basic_operations():
    """基本操作演示"""
    print("\n" + "="*50)
    print("🚀 S7 PLC 基本操作演示")
    print("="*50)
    
    # 创建客户端
    client = S7Client()
    
    # 连接参数 - 请根据实际情况修改
    ip = "*************"  # PLC IP地址
    rack = 0              # Rack号
    slot = 1              # Slot号
    
    print(f"📝 连接参数: IP={ip}, Rack={rack}, Slot={slot}")
    print("💡 如果没有真实PLC，可以使用PLCSIM或其他模拟器")
    
    # 尝试连接
    if client.connect(ip, rack, slot):
        
        # 演示各种数据类型操作
        db_num = 1  # 使用DB1进行测试
        
        print(f"\n📊 在DB{db_num}中演示各种数据类型操作:")
        
        # 布尔值操作
        print("\n🔘 布尔值操作:")
        client.write_bool(db_num, 0, 0, True)   # DB1.DBX0.0 = True
        result = client.read_bool(db_num, 0, 0)
        print(f"   DB{db_num}.DBX0.0 = {result}")
        
        # 整数操作
        print("\n🔢 整数操作:")
        client.write_int(db_num, 2, 1234)       # DB1.DBW2 = 1234
        result = client.read_int(db_num, 2)
        print(f"   DB{db_num}.DBW2 = {result}")
        
        client.write_dint(db_num, 4, 123456)    # DB1.DBD4 = 123456
        result = client.read_dint(db_num, 4)
        print(f"   DB{db_num}.DBD4 = {result}")
        
        # 浮点数操作
        print("\n🔢 浮点数操作:")
        client.write_real(db_num, 8, 3.14159)   # DB1.DBD8 = 3.14159
        result = client.read_real(db_num, 8)
        print(f"   DB{db_num}.DBD8 = {result:.5f}")
        
        # 字符串操作
        print("\n📝 字符串操作:")
        client.write_string(db_num, 12, "Hello S7!", 20)  # DB1.DBString12
        result = client.read_string(db_num, 12)
        print(f"   DB{db_num}.DBString12 = '{result}'")
        
        # 批量读取
        print("\n📦 批量读取:")
        data = client.read_db(db_num, 0, 50)
        if data:
            print(f"   DB{db_num}.0-49 原始数据: {data.hex()}")
        
        # 断开连接
        client.disconnect()
        
    else:
        print("❌ 连接失败，请检查:")
        print("   1. PLC是否在线")
        print("   2. IP地址是否正确")
        print("   3. 网络是否连通")
        print("   4. Rack/Slot参数是否正确")


def demo_monitoring():
    """数据监控演示"""
    print("\n" + "="*50)
    print("📊 S7 PLC 数据监控演示")
    print("="*50)
    
    client = S7Client()
    
    # 连接参数
    ip = "*************"
    
    if client.connect(ip):
        print("🔄 开始监控 DB1.0-20 (按Ctrl+C停止)")
        
        try:
            while True:
                # 读取监控数据
                data = client.read_db(1, 0, 20)
                if data:
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    
                    # 解析不同数据类型
                    bool_val = get_bool(data, 0, 0)
                    int_val = get_int(data, 2) if len(data) >= 4 else 0
                    dint_val = get_dint(data, 4) if len(data) >= 8 else 0
                    real_val = get_real(data, 8) if len(data) >= 12 else 0.0
                    
                    print(f"[{timestamp}] Bool:{bool_val} Int:{int_val} DInt:{dint_val} Real:{real_val:.3f}")
                
                time.sleep(1)  # 每秒更新一次
                
        except KeyboardInterrupt:
            print("\n⏹️ 监控已停止")
            
        client.disconnect()


def main():
    print("🏭 西门子S7 PLC Python通信示例")
    print("📚 基于 python-snap7 库")
    print("🔗 GitHub: https://github.com/gijzelaerr/python-snap7")
    
    while True:
        print("\n" + "="*50)
        print("请选择操作:")
        print("1. 基本操作演示")
        print("2. 数据监控演示") 
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            demo_basic_operations()
        elif choice == "2":
            demo_monitoring()
        elif choice == "3":
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请重新输入")


if __name__ == "__main__":
    main()
