/**
 * ROI检测控制器
 * 负责动态控制指定ROI的检测启停，包括前端截图和后端算法
 */

import { ref, reactive } from 'vue'

export interface ROIDetectionState {
  roi_id: string
  is_active: boolean
  detection_type: 'direction' | 'motion'
  roi_attribute: 'yazhu' | 'pailiao'
  last_activated: number
  last_deactivated: number
}

export interface ROIControlCommand {
  action: 'start' | 'stop' | 'start_batch' | 'stop_batch'
  roi_ids: string[]
  reason?: string
  timestamp: number
}

export function useROIDetectionController() {
  // ROI检测状态管理
  const roiDetectionStates = reactive<Record<string, ROIDetectionState>>({})
  
  // 当前活跃的ROI列表
  const activeROIs = ref<string[]>([])
  
  // 控制命令历史
  const controlHistory = ref<ROIControlCommand[]>([])
  
  // WebSocket发送函数（由外部注入）
  let sendMessageToServer: ((message: any) => void) | null = null
  
  // 日志记录函数（由外部注入）
  let addOperationInfo: ((message: string) => void) | null = null
  
  /**
   * 初始化ROI检测控制器
   */
  const initializeController = (
    sendMessageFn: (message: any) => void,
    logFn: (message: string) => void
  ) => {
    sendMessageToServer = sendMessageFn
    addOperationInfo = logFn
    
    if (addOperationInfo) {
      addOperationInfo('[ROI-CTRL] ROI检测控制器已初始化')
    }
  }
  
  /**
   * 注册ROI到控制器
   */
  const registerROI = (roi: {
    roi_id: string
    attribute: 'yazhu' | 'pailiao'
    algorithm_type: 'direction' | 'motion'
  }) => {
    roiDetectionStates[roi.roi_id] = {
      roi_id: roi.roi_id,
      is_active: false, // 默认关闭
      detection_type: roi.algorithm_type,
      roi_attribute: roi.attribute,
      last_activated: 0,
      last_deactivated: Date.now()
    }
    
    if (addOperationInfo) {
      addOperationInfo(`[ROI-CTRL] 注册ROI: ${roi.roi_id} (${roi.attribute}, ${roi.algorithm_type})`)
    }
  }
  
  /**
   * 启动指定ROI的检测
   */
  const startROIDetection = (roi_id: string, reason?: string) => {
    if (!roiDetectionStates[roi_id]) {
      console.warn(`[ROI-CTRL] ROI ${roi_id} 未注册`)
      return false
    }
    
    if (roiDetectionStates[roi_id].is_active) {
      console.log(`[ROI-CTRL] ROI ${roi_id} 已经处于活跃状态`)
      return true
    }
    
    // 更新状态
    roiDetectionStates[roi_id].is_active = true
    roiDetectionStates[roi_id].last_activated = Date.now()
    
    // 更新活跃列表
    if (!activeROIs.value.includes(roi_id)) {
      activeROIs.value.push(roi_id)
    }
    
    // 发送控制命令到后端
    const command: ROIControlCommand = {
      action: 'start',
      roi_ids: [roi_id],
      reason: reason || '手动启动',
      timestamp: Date.now()
    }
    
    sendControlCommand(command)
    
    if (addOperationInfo) {
      addOperationInfo(`[ROI-CTRL] 启动ROI检测: ${roi_id} (${reason || '手动启动'})`)
    }
    
    return true
  }
  
  /**
   * 停止指定ROI的检测
   */
  const stopROIDetection = (roi_id: string, reason?: string) => {
    if (!roiDetectionStates[roi_id]) {
      console.warn(`[ROI-CTRL] ROI ${roi_id} 未注册`)
      return false
    }
    
    if (!roiDetectionStates[roi_id].is_active) {
      console.log(`[ROI-CTRL] ROI ${roi_id} 已经处于非活跃状态`)
      return true
    }
    
    // 更新状态
    roiDetectionStates[roi_id].is_active = false
    roiDetectionStates[roi_id].last_deactivated = Date.now()
    
    // 更新活跃列表
    const index = activeROIs.value.indexOf(roi_id)
    if (index > -1) {
      activeROIs.value.splice(index, 1)
    }
    
    // 发送控制命令到后端
    const command: ROIControlCommand = {
      action: 'stop',
      roi_ids: [roi_id],
      reason: reason || '手动停止',
      timestamp: Date.now()
    }
    
    sendControlCommand(command)
    
    if (addOperationInfo) {
      addOperationInfo(`[ROI-CTRL] 停止ROI检测: ${roi_id} (${reason || '手动停止'})`)
    }
    
    return true
  }
  
  /**
   * 批量启动ROI检测
   */
  const startBatchROIDetection = (roi_ids: string[], reason?: string) => {
    const successfulStarts: string[] = []
    
    roi_ids.forEach(roi_id => {
      if (startROIDetection(roi_id, reason)) {
        successfulStarts.push(roi_id)
      }
    })
    
    if (addOperationInfo) {
      addOperationInfo(`[ROI-CTRL] 批量启动ROI检测: ${successfulStarts.join(', ')} (${reason || '批量启动'})`)
    }
    
    return successfulStarts
  }
  
  /**
   * 批量停止ROI检测
   */
  const stopBatchROIDetection = (roi_ids: string[], reason?: string) => {
    const successfulStops: string[] = []
    
    roi_ids.forEach(roi_id => {
      if (stopROIDetection(roi_id, reason)) {
        successfulStops.push(roi_id)
      }
    })
    
    if (addOperationInfo) {
      addOperationInfo(`[ROI-CTRL] 批量停止ROI检测: ${successfulStops.join(', ')} (${reason || '批量停止'})`)
    }
    
    return successfulStops
  }
  
  /**
   * 根据属性启动/停止ROI检测
   */
  const controlROIsByAttribute = (
    attribute: 'yazhu' | 'pailiao',
    action: 'start' | 'stop',
    reason?: string
  ) => {
    const targetROIs = Object.values(roiDetectionStates)
      .filter(state => state.roi_attribute === attribute)
      .map(state => state.roi_id)
    
    if (action === 'start') {
      return startBatchROIDetection(targetROIs, reason)
    } else {
      return stopBatchROIDetection(targetROIs, reason)
    }
  }
  
  /**
   * 发送控制命令到后端
   */
  const sendControlCommand = (command: ROIControlCommand) => {
    if (!sendMessageToServer) {
      console.error('[ROI-CTRL] WebSocket发送函数未初始化')
      return
    }
    
    // 记录命令历史
    controlHistory.value.push(command)
    
    // 保持历史记录在合理范围内
    if (controlHistory.value.length > 100) {
      controlHistory.value.splice(0, 50)
    }
    
    // 发送到后端
    sendMessageToServer({
      type: 'roi_detection_control',
      command: command
    })
    
    console.log('[ROI-CTRL] 发送控制命令:', command)
  }
  
  /**
   * 获取ROI检测状态
   */
  const getROIDetectionState = (roi_id: string) => {
    return roiDetectionStates[roi_id] || null
  }
  
  /**
   * 获取所有ROI检测状态
   */
  const getAllROIDetectionStates = () => {
    return { ...roiDetectionStates }
  }
  
  /**
   * 获取指定属性的ROI列表
   */
  const getROIsByAttribute = (attribute: 'yazhu' | 'pailiao') => {
    return Object.values(roiDetectionStates)
      .filter(state => state.roi_attribute === attribute)
      .map(state => state.roi_id)
  }
  
  /**
   * 获取活跃的ROI列表
   */
  const getActiveROIs = () => {
    return [...activeROIs.value]
  }
  
  /**
   * 重置所有ROI状态
   */
  const resetAllROIStates = () => {
    Object.keys(roiDetectionStates).forEach(roi_id => {
      roiDetectionStates[roi_id].is_active = false
      roiDetectionStates[roi_id].last_deactivated = Date.now()
    })
    
    activeROIs.value = []
    
    if (addOperationInfo) {
      addOperationInfo('[ROI-CTRL] 重置所有ROI检测状态')
    }
  }
  
  return {
    // 状态
    roiDetectionStates,
    activeROIs,
    controlHistory,
    
    // 初始化
    initializeController,
    registerROI,
    
    // 单个ROI控制
    startROIDetection,
    stopROIDetection,
    
    // 批量控制
    startBatchROIDetection,
    stopBatchROIDetection,
    controlROIsByAttribute,
    
    // 查询
    getROIDetectionState,
    getAllROIDetectionStates,
    getROIsByAttribute,
    getActiveROIs,
    
    // 重置
    resetAllROIStates
  }
}
