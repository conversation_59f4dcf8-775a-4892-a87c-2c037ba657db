#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI程序 - 不依赖真实PLC连接
"""

import sys
from PySide6.QtWidgets import QApplication, QMessageBox
from s7_plc_tester import S7PLCTester

def main():
    print("启动S7 PLC测试工具...")
    
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = S7PLCTester()
    
    # 显示使用说明
    msg = QMessageBox()
    msg.setWindowTitle("使用说明")
    msg.setText("""S7 PLC 测试工具使用说明:

1. 如果有真实PLC:
   - 设置正确的IP地址、Rack、Slot
   - 点击"连接"按钮

2. 如果没有真实PLC:
   - 点击"启动模拟器"按钮
   - IP地址会自动设为127.0.0.1
   - 然后点击"连接"按钮

3. 连接成功后可以:
   - 在"数据读写"标签页测试读写功能
   - 在"实时监控"标签页监控数据变化

注意: 模拟器功能有限，主要用于测试界面和基本功能。""")
    msg.setStandardButtons(QMessageBox.Ok)
    msg.exec()
    
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
