/* 检测信息组件样式 */
.detection-info {
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px var(--shadow-color);
  transition: all 0.3s ease;
}

.detection-info:hover {
  box-shadow: 0 4px 12px var(--shadow-color-hover);
}

/* 检测信息标题 */
.detection-info__title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

/* 状态指示器 */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.status-indicator__dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-indicator--idle {
  background: rgba(var(--primary-color-rgb), 0.1);
  color: var(--info-color);
  border: 1px solid rgba(var(--primary-color-rgb), 0.2);
}

.status-indicator--idle .status-indicator__dot {
  background: var(--info-color);
}

.status-indicator--running {
  background: rgba(var(--success-color-rgb), 0.1);
  color: var(--success-color);
  border: 1px solid rgba(var(--success-color-rgb), 0.2);
}

.status-indicator--running .status-indicator__dot {
  background: var(--success-color);
}

.status-indicator--error {
  background: rgba(var(--danger-color-rgb), 0.1);
  color: var(--danger-color);
  border: 1px solid rgba(var(--danger-color-rgb), 0.2);
}

.status-indicator--error .status-indicator__dot {
  background: var(--error-color);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 模式选择器 */
.mode-selector {
  margin: 16px 0;
}

.mode-selector__label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color-soft);
  margin-bottom: 8px;
}

.mode-selector__group {
  display: flex;
  gap: 8px;
}

.mode-selector__option {
  flex: 1;
  padding: 8px 16px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--bg-color);
  color: var(--text-color-soft);
  font-size: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mode-selector__option:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.mode-selector__option--active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--text-color-inverse, #ffffff);
}

.mode-selector__option--active:hover {
  background: var(--primary-color-light);
  border-color: var(--primary-color-light);
}

/* 算法显示 */
.algorithm-display {
  margin: 16px 0;
}

.algorithm-display__label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color-soft);
  margin-bottom: 8px;
}

.algorithm-display__value {
  padding: 8px 12px;
  background: var(--bg-color-soft);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 12px;
  color: var(--text-color);
  font-family: 'Courier New', monospace;
}

/* 检测结果 */
.detection-results {
  margin: 16px 0;
}

.detection-results__label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color-soft);
  margin-bottom: 8px;
}

.detection-results__list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--bg-color-mute);
}

.detection-results__item {
  padding: 8px 12px;
  border-bottom: 1px solid var(--border-color);
  font-size: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detection-results__item:last-child {
  border-bottom: none;
}

.detection-results__item--positive {
  background: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
}

.detection-results__item--negative {
  background: rgba(var(--danger-color-rgb), 0.1);
  color: var(--error-color);
}

.detection-results__roi-name {
  font-weight: 500;
}

.detection-results__status {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  background: var(--bg-color-hover);
  color: var(--text-color-mute);
}

.detection-results__status--detected {
  background: rgba(var(--success-color-rgb), 0.2);
  color: var(--success-color);
}

.detection-results__status--clear {
  background: rgba(var(--warning-color-rgb), 0.2);
  color: var(--warning-color);
}

/* 统计信息 */
.detection-stats {
  margin: 16px 0;
}

.detection-stats__label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color-soft);
  margin-bottom: 8px;
}

.detection-stats__grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.detection-stats__item {
  padding: 8px;
  background: var(--bg-color-soft);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  text-align: center;
}

.detection-stats__value {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 4px;
}

.detection-stats__name {
  font-size: 10px;
  color: var(--text-color-mute);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 控制按钮 */
.detection-controls {
  margin: 16px 0;
  display: flex;
  gap: 8px;
}

.detection-controls__button {
  flex: 1;
  padding: 10px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.detection-controls__button--start {
  background: var(--success-color);
  color: var(--text-color-inverse, #ffffff);
}

.detection-controls__button--start:hover {
  background: var(--success-color-light, #85ce61);
}

.detection-controls__button--start:disabled {
  background: var(--text-color-mute);
  cursor: not-allowed;
}

.detection-controls__button--stop {
  background: var(--error-color);
  color: var(--text-color-inverse, #ffffff);
}

.detection-controls__button--stop:hover {
  background: var(--error-color-light, #f78989);
}

.detection-controls__button--stop:disabled {
  background: var(--text-color-mute);
  cursor: not-allowed;
}

/* 紧凑模式 */
.detection-info--compact {
  padding: 12px;
}

.detection-info--compact .detection-info__title {
  font-size: 14px;
  margin-bottom: 12px;
}

.detection-info--compact .mode-selector,
.detection-info--compact .algorithm-display,
.detection-info--compact .detection-results,
.detection-info--compact .detection-stats {
  margin: 12px 0;
}

.detection-info--compact .detection-results__list {
  max-height: 150px;
}

.detection-info--compact .detection-stats__grid {
  grid-template-columns: repeat(4, 1fr);
}

.detection-info--compact .detection-stats__value {
  font-size: 16px;
}

.detection-info--compact .detection-controls__button {
  padding: 8px 12px;
  font-size: 12px;
}

/* 主题切换过渡动画 */
.detection-info,
.status-indicator,
.mode-selector__option,
.algorithm-display__value,
.detection-results__list,
.detection-results__item,
.detection-stats__item,
.detection-controls__button {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* ROI算法统计样式 */
.algorithm-summary {
  margin: 16px 0;
  padding: 12px;
  background: rgba(var(--primary-color-rgb), 0.05);
  border: 1px solid rgba(var(--primary-color-rgb), 0.1);
  border-radius: 6px;
}

.algorithm-summary__label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 12px;
}

/* 算法类型统计 */
.algorithm-summary__stats {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.algorithm-summary__stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: rgba(var(--primary-color-rgb), 0.1);
  border-radius: 4px;
  font-size: 12px;
}

.algorithm-summary__stat-name {
  color: var(--text-color);
  font-weight: 500;
}

.algorithm-summary__stat-count {
  color: var(--primary-color);
  font-weight: 600;
}

/* ROI详细信息 */
.roi-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.roi-details__item {
  padding: 10px;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.roi-details__item:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.1);
}

.roi-details__item--yazhu {
  border-left: 3px solid var(--warning-color);
}

.roi-details__item--pailiao {
  border-left: 3px solid var(--info-color);
}

.roi-details__header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.roi-details__name {
  font-weight: 600;
  color: var(--text-color);
  font-size: 13px;
}

.roi-details__type {
  padding: 2px 6px;
  background: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
}

.roi-details__status {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
  margin-left: auto;
}

.roi-details__status--active {
  background: rgba(var(--success-color-rgb), 0.1);
  color: var(--success-color);
}

.roi-details__status--inactive {
  background: rgba(var(--text-color-rgb), 0.1);
  color: var(--text-color-secondary);
}

.roi-details__params {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.roi-details__param-group {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 12px;
}

.roi-details__param-label {
  color: var(--text-color-secondary);
  font-weight: 500;
  min-width: 60px;
  flex-shrink: 0;
}

.roi-details__param-value {
  color: var(--text-color);
  font-weight: 500;
}

.roi-details__param-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  flex: 1;
}

.roi-details__param-item {
  padding: 2px 6px;
  background: rgba(var(--info-color-rgb), 0.1);
  color: var(--info-color);
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detection-info {
    padding: 12px;
  }

  .mode-selector__group {
    flex-direction: column;
  }

  .detection-stats__grid {
    grid-template-columns: 1fr;
  }

  .detection-controls {
    flex-direction: column;
  }

  .algorithm-summary__stats {
    flex-direction: column;
    gap: 8px;
  }

  .roi-details__header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .roi-details__status {
    margin-left: 0;
  }

  .roi-details__param-group {
    flex-direction: column;
    gap: 4px;
  }

  .roi-details__param-label {
    min-width: auto;
  }
}