<template>
  <div class="video-preview-container">
    <!-- 视频预览区域 -->
    <div class="video-section">
      <div class="video-controls">
        <el-select 
          v-model="selectedVideoSource" 
          placeholder="选择视频源"
          @change="onVideoSourceChange"
          style="width: 200px; margin-right: 10px;"
        >
          <el-option
            v-for="source in videoSources"
            :key="source.id"
            :label="source.name"
            :value="source.id"
          />
        </el-select>
        
        <el-button 
          @click="refreshVideoSources" 
          :loading="isConnecting"
          type="primary"
        >
          刷新视频源
        </el-button>
        
        <el-button 
          @click="startPreview" 
          :disabled="!selectedVideoSource || isPreviewActive"
          type="success"
        >
          开始预览
        </el-button>
        
        <el-button 
          @click="stopPreview" 
          :disabled="!isPreviewActive"
          type="danger"
        >
          停止预览
        </el-button>
      </div>
      
      <!-- 视频播放器容器 -->
      <div 
        ref="videoPlayer"
        class="video-player-container"
        :style="videoContainerStyle"
      >
        <!-- WebSDK视频播放器将在这里渲染 -->
      </div>
    </div>

    <!-- ROI控制区域 -->
    <div class="roi-section">
      <div class="roi-controls">
        <h3>ROI区域管理</h3>
        
        <!-- ROI属性选择 -->
        <div class="roi-attribute-selector">
          <label>ROI属性:</label>
          <el-select 
            v-model="selectedROIAttribute" 
            @change="onROIAttributeChange"
            style="width: 150px; margin-left: 10px;"
          >
            <el-option label="排料检测" value="pailiao" />
            <el-option label="卡料检测" value="kaliao" />
            <el-option label="其他" value="other" />
          </el-select>
        </div>
        
        <!-- ROI操作按钮 -->
        <div class="roi-actions">
          <el-button 
            @click="toggleDrawMode" 
            :type="isDrawingEnabled ? 'danger' : 'primary'"
          >
            {{ isDrawingEnabled ? '停止绘制' : '开始绘制ROI' }}
          </el-button>
          
          <el-button @click="clearROIs" type="warning">
            清除所有ROI
          </el-button>
          
          <el-button @click="saveROIs" type="success">
            保存ROI配置
          </el-button>
          
          <el-button @click="roiLoader.showLoadDialog()" type="info">
            加载已保存ROI
          </el-button>
        </div>
        
        <!-- ROI统计信息 -->
        <div class="roi-stats">
          <span>当前ROI数量: {{ roiCount }}</span>
        </div>
      </div>
      
      <!-- ROI列表 -->
      <div class="roi-list">
        <h4>ROI列表</h4>
        <div v-if="roiList.length === 0" class="no-roi">
          暂无ROI区域
        </div>
        <div v-else>
          <div 
            v-for="roi in roiList" 
            :key="roi.roi_id"
            class="roi-item"
            :class="{ highlighted: highlightedROIId === roi.roi_id }"
            @mouseenter="toggleROIHighlight(roi.roi_id, true)"
            @mouseleave="toggleROIHighlight(roi.roi_id, false)"
          >
            <div class="roi-info">
              <span class="roi-id">{{ roi.roi_id }}</span>
              <span class="roi-attribute">{{ roi.attribute || 'pailiao' }}</span>
            </div>
            <div class="roi-actions">
              <el-button size="small" @click="editROI(roi.roi_id)">编辑</el-button>
              <el-button size="small" type="danger" @click="deleteROI(roi.roi_id)">删除</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 检测控制区域 -->
    <div class="detection-section">
      <div class="detection-controls">
        <h3>检测控制</h3>
        
        <div class="detection-actions">
          <el-button 
            @click="toggleROIDetection" 
            :type="isDetectionActive ? 'danger' : 'success'"
            :disabled="roiList.length === 0"
          >
            {{ isDetectionActive ? '停止检测' : '开始检测' }}
          </el-button>
        </div>
        
        <!-- 检测状态 -->
        <div class="detection-status">
          <span :class="{ active: isDetectionActive }">
            检测状态: {{ isDetectionActive ? '运行中' : '已停止' }}
          </span>
        </div>
        
        <!-- 检测统计 -->
        <div class="detection-stats" v-if="detectionStats">
          <h4>检测统计</h4>
          <div>总检测次数: {{ detectionStats.totalDetections || 0 }}</div>
          <div>异常检测次数: {{ detectionStats.anomalyDetections || 0 }}</div>
        </div>
      </div>
    </div>

    <!-- 全局设置区域 -->
    <div class="settings-section">
      <h3>全局设置</h3>
      <div class="settings-form">
        <div class="setting-item">
          <label>延时时间(秒):</label>
          <el-input-number 
            v-model="globalSettings.delayTime" 
            :min="1" 
            :max="60"
            style="width: 120px;"
          />
        </div>
        
        <div class="setting-item">
          <label>暂停阈值(秒):</label>
          <el-input-number 
            v-model="globalSettings.pauseThreshold" 
            :min="5" 
            :max="300"
            style="width: 120px;"
          />
        </div>
        
        <div class="setting-item">
          <label>冷却时间(秒):</label>
          <el-input-number 
            v-model="globalSettings.cooldownTime" 
            :min="1" 
            :max="60"
            style="width: 120px;"
          />
        </div>
        
        <div class="setting-item">
          <label>卡料延时(秒):</label>
          <el-input-number 
            v-model="globalSettings.cardDelayTime" 
            :min="1" 
            :max="60"
            style="width: 120px;"
          />
        </div>
        
        <el-button @click="onGlobalSettingsApply(globalSettings)" type="primary">
          应用设置
        </el-button>
      </div>
    </div>

    <!-- 操作日志区域 -->
    <div class="log-section">
      <div class="log-header">
        <h3>操作日志</h3>
        <el-button @click="clearOperationInfo" size="small" type="warning">
          清除日志
        </el-button>
      </div>
      
      <div class="log-content">
        <div 
          v-for="(info, index) in operationInfos" 
          :key="index"
          class="log-item"
          :class="{ 
            error: info.includes('ERROR') || info.includes('❌'),
            warning: info.includes('WARNING') || info.includes('⚠️'),
            success: info.includes('✅') || info.includes('成功')
          }"
        >
          {{ info }}
        </div>
      </div>
    </div>

    <!-- ROI加载对话框 -->
    <component 
      :is="roiLoader.component" 
      v-if="roiLoader.visible"
      @load-rois="handleLoadROIs"
      @close="roiLoader.hideLoadDialog()"
    />
    
    <!-- 配置保存对话框 -->
    <component 
      :is="configSaver.component" 
      v-if="configSaver.visible"
      @save-config="handleSaveConfig"
      @close="configSaver.hideDialog()"
    />
  </div>
</template>

<script setup lang="ts">
import { useVideoPreviewMain } from './composables/useVideoPreviewMain'

const {
  // Props和computed
  props, urlDetectionMode, urlTemplateId, urlDetectionGroupId,
  // Configuration
  configParams, templateInfo, dieCasterInfo, detectionGroupInfo,
  // Debug
  DEBUG_ENABLED,
  // WebSDK
  webVideoCtrl, videoSources, selectedVideoSource, isConnecting, isPreviewActive, videoContainerStyle,
  refreshVideoSources, onVideoSourceChange, startPreview, stopPreview,
  // ROI
  roiDrawerInstance, roiList, roiCount, selectedROIAttribute, isDrawingEnabled, highlightedROIId,
  onROIAttributeChange, toggleDrawMode, clearROIs, saveROIs, exportROIs, importROIs,
  loadROIListToVideo, testROILoad, manualInitROI, editROI, deleteROI, toggleROIHighlight,
  clearAttributeROIs, onROIDrawerReady,
  // Detection
  isDetectionActive, roiDetectionResult, detectionStats, roiDetectors, roiDetectionStates,
  startROIDetection, stopROIDetection, toggleROIDetection, onRoiAlgorithmChange, onRoiAlgorithmConfig,
  // Global settings
  globalSettings, onGlobalSettingsApply,
  // Logging
  operationInfos, clearOperationInfo,
  // ROI loading
  roiLoader, handleLoadROIs,
  // Config saving
  configSaver, handleSaveConfig,
  // Video
  videoPlayer, currentVideoSource, onVideoSizeChanged,
  // Debug
  parameterLogViewer, algorithmDebugLog, forceInitROI, checkROIStatus
} = useVideoPreviewMain()
</script>

<style>
@import './styles/video-preview.css';
</style>
