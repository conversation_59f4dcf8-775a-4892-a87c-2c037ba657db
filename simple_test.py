#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的S7连接测试
"""

import socket
import subprocess
import sys

def test_ping(ip):
    """测试ping"""
    print(f"测试PING {ip}...")
    try:
        result = subprocess.run(['ping', '-n', '1', ip], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✓ PING成功")
            return True
        else:
            print("✗ PING失败")
            return False
    except:
        print("✗ PING测试异常")
        return False

def test_port(ip, port):
    """测试端口连通性"""
    print(f"测试端口 {ip}:{port}...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex((ip, port))
        sock.close()
        if result == 0:
            print(f"✓ 端口{port}开放")
            return True
        else:
            print(f"✗ 端口{port}关闭")
            return False
    except:
        print(f"✗ 端口{port}测试异常")
        return False

def test_s7_connection():
    """测试S7连接"""
    print("\n=== S7连接测试 ===")
    
    # 常见的测试IP和参数
    test_configs = [
        ("127.0.0.1", 0, 1),      # 本地模拟器
        ("*************", 0, 1),  # 常见配置1
        ("*************", 0, 2),  # 常见配置2
        ("*************", 0, 1),  # 常见配置3
        ("**********", 0, 1),     # 常见配置4
    ]
    
    try:
        import snap7
        
        for ip, rack, slot in test_configs:
            print(f"\n测试 {ip} (Rack:{rack}, Slot:{slot})")
            
            # 先测试网络连通性
            if not test_ping(ip):
                continue
                
            # 测试S7端口
            if not test_port(ip, 102):
                continue
            
            # 测试S7连接
            try:
                client = snap7.client.Client()
                print("尝试S7连接...")
                client.connect(ip, rack, slot)
                
                if client.get_connected():
                    print("✓ S7连接成功!")
                    
                    # 获取CPU信息
                    try:
                        cpu_info = client.get_cpu_info()
                        print(f"CPU: {cpu_info.ModuleTypeName}")
                    except:
                        print("无法获取CPU信息")
                    
                    client.disconnect()
                    return True
                else:
                    print("✗ S7连接失败")
                    
            except Exception as e:
                print(f"✗ S7连接异常: {e}")
                
    except ImportError:
        print("✗ snap7库未安装")
        return False
    
    return False

def main():
    print("S7 PLC 简单连接测试")
    print("=" * 30)
    
    # 显示本机IP
    try:
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        print(f"本机IP: {local_ip}")
    except:
        print("无法获取本机IP")
    
    # 测试S7连接
    success = test_s7_connection()
    
    print("\n" + "=" * 30)
    if success:
        print("✓ 找到可用的PLC连接")
    else:
        print("✗ 未找到可用的PLC连接")
        print("\n可能的原因:")
        print("1. PLC未连接或IP地址错误")
        print("2. 网络不通")
        print("3. PLC未启用以太网通信")
        print("4. 防火墙阻止连接")
        print("5. Rack/Slot参数错误")
        
        print("\n建议:")
        print("1. 检查PLC的IP地址设置")
        print("2. 确保PLC和电脑在同一网段")
        print("3. 检查PLC程序中的通信配置")
        print("4. 尝试使用PLC厂商的软件先测试连接")

if __name__ == "__main__":
    main()
