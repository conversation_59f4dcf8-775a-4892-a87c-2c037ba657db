#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
纯Python实现的S7通信客户端
参考: https://github.com/klsecservices/s7comm-python
不依赖外部库，仅使用Python标准库
"""

import socket
import struct
import time
from datetime import datetime


class S7Protocol:
    """S7协议常量和工具函数"""
    
    # S7协议常量
    TPKT_VERSION = 0x03
    COTP_CONNECT_REQUEST = 0xE0
    COTP_DATA = 0xF0
    S7_PROTOCOL_ID = 0x32
    
    # S7功能码
    S7_FUNC_READ = 0x04
    S7_FUNC_WRITE = 0x05
    S7_FUNC_SETUP_COMM = 0xF0
    
    # 数据类型
    S7_TYPE_BIT = 0x01
    S7_TYPE_BYTE = 0x02
    S7_TYPE_WORD = 0x04
    S7_TYPE_DWORD = 0x06
    
    @staticmethod
    def create_tpkt_header(length):
        """创建TPKT头部"""
        return struct.pack('>BBH', S7Protocol.TPKT_VERSION, 0x00, length)
    
    @staticmethod
    def create_cotp_connect_request():
        """创建COTP连接请求"""
        return bytes([
            0x11,  # Length
            0xE0,  # PDU Type: Connect Request
            0x00, 0x00,  # Destination Reference
            0x00, 0x01,  # Source Reference
            0x00,  # Class + Option
            0xC0, 0x01, 0x0A,  # Parameter: TPDU Size
            0xC1, 0x02, 0x01, 0x00,  # Parameter: Source TSAP
            0xC2, 0x02, 0x01, 0x02   # Parameter: Destination TSAP
        ])
    
    @staticmethod
    def create_s7_setup_comm():
        """创建S7通信设置请求"""
        header = bytes([
            0x02, 0xF0, 0x80,  # COTP Header
            0x32,  # S7 Protocol ID
            0x01,  # Message Type: Job Request
            0x00, 0x00,  # Reserved
            0x00, 0x01,  # PDU Reference
            0x00, 0x08,  # Parameter Length
            0x00, 0x00,  # Data Length
            0xF0,  # Function: Setup Communication
            0x00,  # Reserved
            0x00, 0x01,  # Max AMQ Calling
            0x00, 0x01,  # Max AMQ Called
            0x01, 0xE0   # PDU Length
        ])
        return header


class PureS7Client:
    """纯Python S7客户端"""
    
    def __init__(self):
        self.socket = None
        self.connected = False
        self.pdu_reference = 1
        
    def connect(self, ip, port=102, rack=0, slot=1):
        """连接到S7 PLC"""
        try:
            print(f"🔗 连接到 {ip}:{port}")
            
            # 创建TCP连接
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)
            self.socket.connect((ip, port))
            
            # 发送COTP连接请求
            cotp_request = S7Protocol.create_cotp_connect_request()
            tpkt_length = 4 + len(cotp_request)
            tpkt_header = S7Protocol.create_tpkt_header(tpkt_length)
            
            request = tpkt_header + cotp_request
            print(f"📤 发送COTP连接请求: {request.hex()}")
            self.socket.send(request)
            
            # 接收COTP连接响应
            response = self.socket.recv(1024)
            print(f"📥 收到COTP响应: {response.hex()}")
            
            if len(response) >= 22 and response[5] == 0x11 and response[6] == 0xD0:
                print("✅ COTP连接成功")
                
                # 发送S7通信设置
                s7_setup = S7Protocol.create_s7_setup_comm()
                tpkt_length = 4 + len(s7_setup)
                tpkt_header = S7Protocol.create_tpkt_header(tpkt_length)
                
                request = tpkt_header + s7_setup
                print(f"📤 发送S7设置请求: {request.hex()}")
                self.socket.send(request)
                
                # 接收S7设置响应
                response = self.socket.recv(1024)
                print(f"📥 收到S7设置响应: {response.hex()}")
                
                if len(response) >= 20 and response[17] == 0xF0:
                    print("✅ S7通信设置成功")
                    self.connected = True
                    return True
                else:
                    print("❌ S7通信设置失败")
                    
            else:
                print("❌ COTP连接失败")
                
        except Exception as e:
            print(f"❌ 连接异常: {e}")
            
        return False
    
    def disconnect(self):
        """断开连接"""
        if self.socket:
            self.socket.close()
            self.socket = None
        self.connected = False
        print("🔌 已断开连接")
    
    def create_read_request(self, db_number, start_address, length):
        """创建读取请求"""
        # S7 Header
        header = struct.pack('>BBHHHBB',
            0x32,  # Protocol ID
            0x01,  # Message Type: Job Request
            0x00,  # Reserved
            self.pdu_reference,  # PDU Reference
            0x0E,  # Parameter Length
            0x00,  # Data Length
            0x04   # Function: Read Var
        )
        
        # Parameter
        parameter = struct.pack('>BBB',
            0x01,  # Item Count
            0x12,  # Variable Specification
            0x0A   # Length of following address specification
        )
        
        # Address specification for DB
        address = struct.pack('>BBHBH',
            0x10,  # Syntax ID: S7ANY
            S7Protocol.S7_TYPE_BYTE,  # Transport Size
            length,  # Length
            db_number,  # DB Number
            0x84,  # Area: DB
        )
        
        # Start address (bit address)
        bit_address = start_address * 8
        address += struct.pack('>I', bit_address)[1:]  # 3 bytes
        
        self.pdu_reference += 1
        return header + parameter + address
    
    def read_db(self, db_number, start_address, length):
        """读取DB数据块"""
        if not self.connected:
            print("❌ 未连接到PLC")
            return None
            
        try:
            # 创建读取请求
            s7_request = self.create_read_request(db_number, start_address, length)
            
            # 添加COTP头部
            cotp_header = bytes([0x02, 0xF0, 0x80])
            request_data = cotp_header + s7_request
            
            # 添加TPKT头部
            tpkt_length = 4 + len(request_data)
            tpkt_header = S7Protocol.create_tpkt_header(tpkt_length)
            
            full_request = tpkt_header + request_data
            
            print(f"📤 发送读取请求 DB{db_number}.{start_address}({length}): {full_request.hex()}")
            self.socket.send(full_request)
            
            # 接收响应
            response = self.socket.recv(1024)
            print(f"📥 收到读取响应: {response.hex()}")
            
            # 解析响应
            if len(response) >= 25:
                # 检查错误码
                if response[17] == 0x04 and response[21] == 0xFF:
                    # 提取数据
                    data_length = struct.unpack('>H', response[23:25])[0] // 8
                    data = response[25:25+data_length]
                    print(f"✅ 读取成功: {data.hex()}")
                    return data
                else:
                    print(f"❌ 读取失败，错误码: {response[21]:02x}")
            else:
                print("❌ 响应格式错误")
                
        except Exception as e:
            print(f"❌ 读取异常: {e}")
            
        return None
    
    def parse_data(self, data):
        """解析数据为不同类型"""
        if not data:
            return {}
            
        result = {}
        
        if len(data) >= 1:
            result['byte_0'] = data[0]
            
        if len(data) >= 2:
            result['word_0'] = struct.unpack('>H', data[0:2])[0]
            result['int_0'] = struct.unpack('>h', data[0:2])[0]
            
        if len(data) >= 4:
            result['dword_0'] = struct.unpack('>I', data[0:4])[0]
            result['dint_0'] = struct.unpack('>i', data[0:4])[0]
            result['real_0'] = struct.unpack('>f', data[0:4])[0]
            
        return result


def demo_pure_s7():
    """纯Python S7通信演示"""
    print("🏭 纯Python S7通信演示")
    print("📚 不依赖外部库，仅使用Python标准库")
    print("="*50)
    
    client = PureS7Client()
    
    # 连接参数
    ip = input("请输入PLC IP地址 (默认: *************): ").strip()
    if not ip:
        ip = "*************"
    
    if client.connect(ip):
        print("\n🎉 连接成功！开始测试读取操作...")
        
        # 测试读取DB1
        db_number = 1
        start_address = 0
        length = 10
        
        data = client.read_db(db_number, start_address, length)
        
        if data:
            print(f"\n📊 DB{db_number}.{start_address} 数据解析:")
            parsed = client.parse_data(data)
            
            for key, value in parsed.items():
                if 'real' in key:
                    print(f"   {key}: {value:.3f}")
                else:
                    print(f"   {key}: {value}")
        
        # 监控模式
        monitor = input("\n是否开启监控模式? (y/n): ").strip().lower()
        if monitor == 'y':
            print("🔄 开始监控 (按Ctrl+C停止)")
            try:
                while True:
                    data = client.read_db(db_number, start_address, length)
                    if data:
                        timestamp = datetime.now().strftime("%H:%M:%S")
                        parsed = client.parse_data(data)
                        print(f"[{timestamp}] Byte:{parsed.get('byte_0', 0)} Word:{parsed.get('word_0', 0)} Real:{parsed.get('real_0', 0):.3f}")
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n⏹️ 监控已停止")
        
        client.disconnect()
    else:
        print("❌ 连接失败")


if __name__ == "__main__":
    demo_pure_s7()
