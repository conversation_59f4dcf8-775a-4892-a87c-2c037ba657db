<template>
  <el-dialog
    v-model="dialogVisible"
    title="加载已保存的ROI"
    width="900px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="roi-load-container">
      <!-- 搜索和筛选区域 -->
      <div class="filter-section">
        <el-row :gutter="16">
          <el-col :span="8">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索ROI名称或ID"
              prefix-icon="Search"
              clearable
              @input="handleSearch"
            />
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="filterType"
              placeholder="筛选类型"
              clearable
              @change="handleFilter"
            >
              <el-option label="全部" value="" />
              <el-option label="压铸机(yazhu)" value="yazhu" />
              <el-option label="排料口(pailiao)" value="pailiao" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="filterAlgorithm"
              placeholder="筛选算法"
              clearable
              @change="handleFilter"
            >
              <el-option label="全部" value="" />
              <el-option label="方向检测" value="direction" />
              <el-option label="运动检测" value="motion" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="refreshROIList" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- ROI列表表格 -->
      <div class="table-section">
        <ROIListTable
          :roi-list="filteredROIList"
          :loading="loading"
          :selected-rois="selectedROIs"
          @selection-change="handleSelectionChange"
          @preview="handlePreview"
        />
      </div>

      <!-- 操作统计 -->
      <div class="stats-section">
        <el-alert
          :title="`共找到 ${filteredROIList.length} 个ROI，已选择 ${selectedROIs.length} 个`"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <!-- ROI预览 -->
      <div v-if="previewROI" class="preview-section">
        <ROIPreview :roi-data="previewROI" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleLoadSelected"
          :disabled="selectedROIs.length === 0"
          :loading="loadingROIs"
        >
          加载选中的ROI ({{ selectedROIs.length }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import ROIListTable from './ROIListTable.vue'
import ROIPreview from './ROIPreview.vue'

// Props
interface Props {
  visible: boolean
  currentVideoSource?: any
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  currentVideoSource: null
})

// Emits
interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'load-rois', rois: any[]): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const loading = ref(false)
const loadingROIs = ref(false)
const roiList = ref<any[]>([])
const selectedROIs = ref<any[]>([])
const previewROI = ref<any>(null)

// 搜索和筛选
const searchKeyword = ref('')
const filterType = ref('')
const filterAlgorithm = ref('')

// 计算属性：过滤后的ROI列表
const filteredROIList = computed(() => {
  let filtered = [...roiList.value]

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(roi => 
      roi.roi_id?.toLowerCase().includes(keyword) ||
      roi.roi_name?.toLowerCase().includes(keyword)
    )
  }

  // 类型筛选
  if (filterType.value) {
    filtered = filtered.filter(roi => roi.roi_type === filterType.value)
  }

  // 算法筛选
  if (filterAlgorithm.value) {
    filtered = filtered.filter(roi => {
      const algorithmType = roi.algorithm_params?.type
      if (filterAlgorithm.value === 'direction') {
        return algorithmType === 'direction'
      } else if (filterAlgorithm.value === 'motion') {
        return algorithmType === 'motion' || algorithmType === 'background_subtraction' || algorithmType === 'frame_difference'
      }
      return true
    })
  }

  return filtered
})

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible) {
    loadROIList()
  } else {
    // 重置状态
    selectedROIs.value = []
    previewROI.value = null
    searchKeyword.value = ''
    filterType.value = ''
    filterAlgorithm.value = ''
  }
})

// 加载ROI列表
const loadROIList = async () => {
  if (!props.currentVideoSource) {
    ElMessage.warning('请先选择视频源')
    return
  }

  loading.value = true
  try {
    const response = await fetch('/api/roi-config/load-by-video-source', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        video_source_id: props.currentVideoSource.id,
        video_source_path: props.currentVideoSource.path
      })
    })

    if (response.ok) {
      const result = await response.json()
      roiList.value = result.data || []
      ElMessage.success(`加载到 ${roiList.value.length} 个已保存的ROI`)
    } else {
      const error = await response.json()
      ElMessage.error(`加载ROI列表失败: ${error.detail}`)
      roiList.value = []
    }
  } catch (error) {
    ElMessage.error(`加载ROI列表异常: ${error}`)
    roiList.value = []
  } finally {
    loading.value = false
  }
}

// 刷新ROI列表
const refreshROIList = () => {
  loadROIList()
}

// 处理搜索
const handleSearch = () => {
  // 搜索逻辑在计算属性中处理
}

// 处理筛选
const handleFilter = () => {
  // 筛选逻辑在计算属性中处理
}

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedROIs.value = selection
}

// 处理预览
const handlePreview = (roi: any) => {
  previewROI.value = roi
}

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false
}

// 处理加载选中的ROI
const handleLoadSelected = async () => {
  if (selectedROIs.value.length === 0) {
    ElMessage.warning('请先选择要加载的ROI')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要加载选中的 ${selectedROIs.value.length} 个ROI吗？这将清除当前的ROI配置。`,
      '确认加载',
      {
        type: 'warning',
        confirmButtonText: '确定加载',
        cancelButtonText: '取消'
      }
    )

    loadingROIs.value = true
    
    // 发射加载事件
    emit('load-rois', selectedROIs.value)
    
    // 关闭对话框
    dialogVisible.value = false
    
  } catch (error) {
    // 用户取消操作
  } finally {
    loadingROIs.value = false
  }
}
</script>

<style scoped>
.roi-load-container {
  max-height: 600px;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 16px;
  padding: 16px;
  background-color: var(--bg-color-soft, #f5f7fa);
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.table-section {
  margin-bottom: 16px;
}

.stats-section {
  margin-bottom: 16px;
}

.preview-section {
  margin-top: 16px;
  padding: 16px;
  border: 1px solid var(--border-color, #e4e7ed);
  border-radius: 4px;
  background-color: var(--bg-color-soft, #fafafa);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

/* 暗色模式增强支持 */
.dark-theme .filter-section {
  background-color: var(--bg-color-mute);
  border: 1px solid var(--border-color);
}

.dark-theme .preview-section {
  background-color: var(--bg-color-mute);
  border-color: var(--border-color);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
