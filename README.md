# S7 PLC 测试工具

这是一个使用 PySide6 和 snap7 库开发的西门子 S7 PLC 通信测试工具。

## 功能特性

- **PLC连接测试**: 支持连接西门子S7系列PLC
- **数据读写**: 支持读写DB数据块
- **实时监控**: 实时监控PLC数据变化
- **多种数据类型**: 支持BYTE、WORD、DWORD、INT、DINT、REAL等数据类型
- **图形界面**: 友好的GUI界面，操作简单

## 安装要求

### 系统要求
- Python 3.7+
- Windows/Linux/macOS

### 依赖库安装

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install PySide6 python-snap7
```

### snap7 库安装注意事项

**Windows系统**:
1. 下载 snap7 库: https://snap7.sourceforge.net/
2. 将 `snap7.dll` 放到系统PATH路径或程序目录下

**Linux系统**:
```bash
sudo apt-get install libsnap7-1 libsnap7-dev
```

**macOS系统**:
```bash
brew install snap7
```

## 使用方法

### 启动程序
```bash
python s7_plc_tester.py
```

### 连接设置
1. **IP地址**: 输入PLC的IP地址 (例如: *************)
2. **Rack**: 通常为0 (S7-300/400) 或0 (S7-1200/1500)
3. **Slot**: 
   - S7-300/400: 通常为2
   - S7-1200/1500: 通常为1
4. 点击"连接"按钮

### 数据读写功能

#### 读取数据
1. 选择"数据读写"标签页
2. 设置数据块号、起始地址、读取长度
3. 点击"读取"按钮
4. 查看读取结果，包括原始HEX数据和各种数据类型解析

#### 写入数据
1. 设置数据块号、起始地址
2. 选择数据类型 (BYTE/WORD/DWORD/INT/DINT/REAL)
3. 输入要写入的数值
4. 点击"写入"按钮

### 实时监控功能
1. 选择"实时监控"标签页
2. 设置监控的数据块、起始地址、长度
3. 点击"开始监控"
4. 实时查看数据变化 (每秒更新一次)

## 支持的PLC型号

- S7-200 (需要以太网模块)
- S7-300 系列
- S7-400 系列  
- S7-1200 系列
- S7-1500 系列

## 数据类型说明

| 类型 | 大小 | 范围 | 说明 |
|------|------|------|------|
| BYTE | 1字节 | 0-255 | 无符号8位整数 |
| WORD | 2字节 | 0-65535 | 无符号16位整数 |
| DWORD | 4字节 | 0-4294967295 | 无符号32位整数 |
| INT | 2字节 | -32768到32767 | 有符号16位整数 |
| DINT | 4字节 | -2147483648到2147483647 | 有符号32位整数 |
| REAL | 4字节 | IEEE 754 | 32位浮点数 |

## 常见问题

### 连接失败
1. 检查网络连通性: `ping PLC_IP`
2. 检查PLC是否允许外部连接
3. 确认Rack和Slot参数正确
4. 检查防火墙设置

### 读写失败
1. 确认数据块存在
2. 检查地址范围是否正确
3. 确认PLC程序中数据块的访问权限

### snap7库问题
1. Windows: 确保snap7.dll在系统路径中
2. Linux: 安装libsnap7开发包
3. 检查snap7库版本兼容性

## 开发说明

### 项目结构
```
s7_plc_tester.py    # 主程序文件
requirements.txt    # 依赖库列表
README.md          # 使用说明
```

### 主要类说明
- `S7PLCTester`: 主窗口类，包含所有GUI组件和PLC通信逻辑
- `S7ConnectionThread`: 连接线程类，用于异步连接PLC

## 许可证

本项目仅供学习和测试使用。

## 联系方式

如有问题或建议，请提交Issue。
