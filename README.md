# 西门子S7 PLC Python通信示例

这个项目提供了多种方式来与西门子S7 PLC进行通信的Python示例代码。

## 🚀 快速开始

### 方法1: 使用python-snap7库 (推荐)

```bash
# 安装依赖
pip install python-snap7

# 运行示例
python s7_client_demo.py
```

### 方法2: 纯Python实现 (无外部依赖)

```bash
# 直接运行，无需安装额外依赖
python pure_s7_client.py
```

## 📚 推荐的开源项目

### 1. python-snap7 ⭐⭐⭐⭐⭐
- **GitHub**: https://github.com/gijzelaerr/python-snap7
- **特点**: 最成熟、功能最完整的Python S7库
- **支持**: S7-200/300/400/1200/1500全系列
- **优势**: 
  - 基于成熟的snap7 C库
  - 文档完善，社区活跃
  - 支持所有S7数据类型
  - 性能优秀

### 2. s7comm-python ⭐⭐⭐⭐
- **GitHub**: https://github.com/klsecservices/s7comm-python
- **特点**: 纯Python实现，不依赖外部C库
- **优势**:
  - 代码简洁，易于理解和修改
  - 跨平台兼容性好
  - 适合学习S7协议

### 3. pys7comm ⭐⭐⭐
- **GitHub**: https://github.com/dariuszlee/pys7comm
- **特点**: 轻量级S7通信库
- **适合**: 快速原型开发

### 4. siemens-s7-python ⭐⭐⭐
- **GitHub**: https://github.com/thomas-v2/siemens-s7-python
- **特点**: 专注于S7-1200/1500系列
- **优势**: 支持异步操作

## 📁 文件说明

- `s7_client_demo.py` - 基于python-snap7的完整示例
- `pure_s7_client.py` - 纯Python实现的S7客户端
- `requirements.txt` - 依赖库列表

## 🔧 功能特性

### s7_client_demo.py 功能
- ✅ PLC连接/断开
- ✅ 读写布尔值 (BOOL)
- ✅ 读写整数 (INT, DINT)
- ✅ 读写浮点数 (REAL)
- ✅ 读写字符串 (STRING)
- ✅ 批量数据读取
- ✅ 实时数据监控
- ✅ 错误处理和重连

### pure_s7_client.py 功能
- ✅ 基础TCP连接
- ✅ S7协议握手
- ✅ DB数据块读取
- ✅ 数据类型解析
- ✅ 实时监控模式

## 🏭 支持的PLC型号

| PLC系列 | python-snap7 | pure_s7_client |
|---------|--------------|----------------|
| S7-200  | ✅ (需以太网模块) | ✅ |
| S7-300  | ✅ | ✅ |
| S7-400  | ✅ | ✅ |
| S7-1200 | ✅ | ✅ |
| S7-1500 | ✅ | ✅ |

## 📖 使用示例

### 基本连接和读写

```python
from s7_client_demo import S7Client

# 创建客户端
client = S7Client()

# 连接PLC
if client.connect("192.168.1.100", rack=0, slot=1):
    
    # 读写不同数据类型
    client.write_bool(1, 0, 0, True)        # DB1.DBX0.0 = True
    client.write_int(1, 2, 1234)            # DB1.DBW2 = 1234
    client.write_real(1, 4, 3.14159)        # DB1.DBD4 = 3.14159
    
    # 读取数据
    bool_val = client.read_bool(1, 0, 0)
    int_val = client.read_int(1, 2)
    real_val = client.read_real(1, 4)
    
    print(f"Bool: {bool_val}, Int: {int_val}, Real: {real_val}")
    
    # 断开连接
    client.disconnect()
```

### 实时监控

```python
import time
from datetime import datetime

# 连接后开始监控
while True:
    data = client.read_db(1, 0, 20)  # 读取DB1.0-19
    if data:
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] 数据: {data.hex()}")
    time.sleep(1)
```

## ⚙️ 连接参数说明

| 参数 | S7-300/400 | S7-1200/1500 | 说明 |
|------|------------|--------------|------|
| IP   | PLC的IP地址 | PLC的IP地址 | 网络地址 |
| Rack | 0 | 0 | 机架号 |
| Slot | 2 | 1 | 插槽号 |
| Port | 102 | 102 | 通信端口 |

## 🔍 故障排除

### 连接失败
1. **检查网络连通性**: `ping PLC_IP`
2. **检查端口**: `telnet PLC_IP 102`
3. **确认PLC设置**: 
   - 启用以太网通信
   - 允许外部连接
   - 检查防火墙设置

### 读写失败
1. **确认数据块存在**: 在PLC程序中创建对应的DB块
2. **检查地址范围**: 确保地址在DB块范围内
3. **检查访问权限**: 确保DB块允许外部访问

### snap7库问题
1. **Windows**: 下载snap7.dll放到系统路径
2. **Linux**: `sudo apt-get install libsnap7-1 libsnap7-dev`
3. **macOS**: `brew install snap7`

## 📞 技术支持

如果遇到问题，可以参考：
1. [python-snap7 官方文档](https://python-snap7.readthedocs.io/)
2. [西门子S7协议文档](https://support.industry.siemens.com/)
3. [GitHub Issues](https://github.com/gijzelaerr/python-snap7/issues)

## 📄 许可证

本项目仅供学习和参考使用。
