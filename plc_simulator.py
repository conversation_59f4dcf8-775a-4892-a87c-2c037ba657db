#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的S7 PLC模拟器
用于测试S7通信程序
"""

import socket
import threading
import struct
import time
import random
from datetime import datetime

class S7PLCSimulator:
    def __init__(self, host='127.0.0.1', port=102):
        self.host = host
        self.port = port
        self.running = False
        self.server_socket = None
        
        # 模拟的数据块存储 (DB块)
        self.data_blocks = {}
        
        # 初始化一些测试数据
        self.init_test_data()
        
    def init_test_data(self):
        """初始化测试数据"""
        # DB1: 包含各种数据类型的测试数据
        db1_data = bytearray(100)
        
        # 在DB1中设置一些测试值
        struct.pack_into('>H', db1_data, 0, 1234)      # WORD at DB1.0
        struct.pack_into('>h', db1_data, 2, -567)      # INT at DB1.2
        struct.pack_into('>I', db1_data, 4, 123456)    # DWORD at DB1.4
        struct.pack_into('>i', db1_data, 8, -123456)   # DINT at DB1.8
        struct.pack_into('>f', db1_data, 12, 3.14159)  # REAL at DB1.12
        
        self.data_blocks[1] = db1_data
        
        # DB2: 动态变化的数据
        db2_data = bytearray(50)
        self.data_blocks[2] = db2_data
        
        print("初始化测试数据完成")
        
    def update_dynamic_data(self):
        """更新动态数据"""
        while self.running:
            try:
                # 更新DB2中的动态数据
                if 2 in self.data_blocks:
                    db2 = self.data_blocks[2]
                    
                    # 模拟传感器数据
                    temp = 20 + random.uniform(-5, 15)  # 温度 15-35度
                    pressure = 1000 + random.uniform(-50, 100)  # 压力
                    counter = int(time.time()) % 65536  # 计数器
                    
                    struct.pack_into('>f', db2, 0, temp)      # 温度
                    struct.pack_into('>f', db2, 4, pressure)  # 压力
                    struct.pack_into('>H', db2, 8, counter)   # 计数器
                    
                time.sleep(1)  # 每秒更新一次
            except:
                break
                
    def handle_s7_request(self, data):
        """处理S7请求"""
        try:
            if len(data) < 4:
                return None

            print(f"处理数据: {data.hex()}")

            # 检查TPKT头部
            if data[0] == 0x03 and data[1] == 0x00:
                # 这是一个TPKT包

                # 检查是否是COTP连接请求
                if len(data) >= 22 and data[5] == 0x11 and data[6] == 0xe0:
                    print("收到COTP连接请求")
                    return self.create_connect_response()

                # 检查是否是ISO连接请求 (另一种格式)
                elif len(data) >= 7 and data[4] == 0x11 and data[5] == 0xe0:
                    print("收到ISO连接请求")
                    return self.create_connect_response()

                # 检查是否是S7通信请求
                elif len(data) >= 19 and data[7] == 0x32:
                    print("收到S7通信请求")

                    # 检查功能码
                    if len(data) >= 20:
                        function_code = data[18]
                        if function_code == 0x04:  # 读请求
                            return self.create_read_response(data)
                        elif function_code == 0x05:  # 写请求
                            return self.create_write_response(data)
                        else:
                            print(f"未知功能码: {function_code:02x}")

            return None

        except Exception as e:
            print(f"处理请求错误: {e}")
            return None
            
    def create_connect_response(self):
        """创建连接响应"""
        # S7连接确认响应 (简化版)
        response = bytearray([
            0x03, 0x00, 0x00, 0x16,  # TPKT Header
            0x11, 0xE0, 0x00, 0x00, 0x00, 0x01, 0x00, 0xC0, 0x01, 0x0A,
            0xC1, 0x02, 0x01, 0x00, 0xC2, 0x02, 0x01, 0x02
        ])
        return bytes(response)
        
    def create_read_response(self, request):
        """创建读响应"""
        # 简化的读响应，返回一些测试数据
        test_data = b'\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0A'
        
        response = bytearray([
            0x03, 0x00, 0x00, 0x1B,  # TPKT Header
            0x02, 0xF0, 0x80,        # COTP Header
            0x32, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00,
            0xF0, 0x00, 0x00, 0x01, 0xFF, 0x04, 0x00, 0x08
        ])
        
        response.extend(test_data[:8])  # 添加数据
        
        # 更新长度
        response[2:4] = struct.pack('>H', len(response))
        
        return bytes(response)
        
    def create_write_response(self, request):
        """创建写响应"""
        response = bytearray([
            0x03, 0x00, 0x00, 0x16,  # TPKT Header
            0x02, 0xF0, 0x80,        # COTP Header
            0x32, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01,
            0x00, 0x01, 0xFF
        ])
        return bytes(response)
        
    def handle_client(self, client_socket, addr):
        """处理客户端连接"""
        print(f"客户端连接: {addr}")
        
        try:
            while self.running:
                data = client_socket.recv(1024)
                if not data:
                    break
                    
                print(f"收到数据: {data.hex()}")
                
                # 处理请求
                response = self.handle_s7_request(data)
                if response:
                    client_socket.send(response)
                    print(f"发送响应: {response.hex()}")
                    
        except Exception as e:
            print(f"客户端处理错误: {e}")
        finally:
            client_socket.close()
            print(f"客户端断开: {addr}")
            
    def start(self):
        """启动模拟器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(5)
            
            self.running = True
            
            print(f"S7 PLC模拟器启动: {self.host}:{self.port}")
            print("等待客户端连接...")
            
            # 启动动态数据更新线程
            update_thread = threading.Thread(target=self.update_dynamic_data)
            update_thread.daemon = True
            update_thread.start()
            
            while self.running:
                try:
                    client_socket, addr = self.server_socket.accept()
                    
                    # 为每个客户端创建处理线程
                    client_thread = threading.Thread(
                        target=self.handle_client, 
                        args=(client_socket, addr)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except socket.error:
                    if self.running:
                        print("接受连接时出错")
                    break
                    
        except Exception as e:
            print(f"启动模拟器失败: {e}")
        finally:
            self.stop()
            
    def stop(self):
        """停止模拟器"""
        self.running = False
        if self.server_socket:
            self.server_socket.close()
        print("模拟器已停止")

def main():
    print("S7 PLC 模拟器")
    print("=" * 30)

    # 创建并启动模拟器
    simulator = S7PLCSimulator()

    try:
        print("正在启动模拟器...")
        simulator.start()
    except KeyboardInterrupt:
        print("\n收到停止信号")
        simulator.stop()
    except Exception as e:
        print(f"模拟器运行错误: {e}")
        simulator.stop()

if __name__ == "__main__":
    main()
