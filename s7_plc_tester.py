#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
S7 PLC 测试工具
使用 PySide6 + snap7 库
"""

import sys
import struct
from datetime import datetime
from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                               QWidget, QPushButton, QLineEdit, QTextEdit, QLabel, 
                               QGroupBox, QGridLayout, QComboBox, QSpinBox, QMessageBox,
                               QTabWidget, QTableWidget, QTableWidgetItem, QHeaderView)
from PySide6.QtCore import QTimer, Qt, QThread, Signal
from PySide6.QtGui import QFont

try:
    import snap7
    from snap7.util import *
except ImportError:
    print("请安装 snap7 库: pip install python-snap7")
    sys.exit(1)


class S7ConnectionThread(QThread):
    """S7连接线程"""
    connection_result = Signal(bool, str)
    
    def __init__(self, ip, rack, slot):
        super().__init__()
        self.ip = ip
        self.rack = rack
        self.slot = slot
        
    def run(self):
        try:
            client = snap7.client.Client()
            client.connect(self.ip, self.rack, self.slot)
            if client.get_connected():
                cpu_info = client.get_cpu_info()
                info_text = f"连接成功!\nCPU型号: {cpu_info.ModuleTypeName}\nCPU版本: {cpu_info.ModuleVersion}"
                client.disconnect()
                self.connection_result.emit(True, info_text)
            else:
                self.connection_result.emit(False, "连接失败")
        except Exception as e:
            self.connection_result.emit(False, f"连接错误: {str(e)}")


class S7PLCTester(QMainWindow):
    def __init__(self):
        super().__init__()
        self.client = snap7.client.Client()
        self.connected = False
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_monitor_data)
        
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("S7 PLC 测试工具")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 连接设置组
        connection_group = self.create_connection_group()
        main_layout.addWidget(connection_group)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 数据读写标签页
        data_tab = self.create_data_tab()
        tab_widget.addTab(data_tab, "数据读写")
        
        # 监控标签页
        monitor_tab = self.create_monitor_tab()
        tab_widget.addTab(monitor_tab, "实时监控")
        
        main_layout.addWidget(tab_widget)
        
        # 日志区域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setFont(QFont("Consolas", 9))
        main_layout.addWidget(QLabel("操作日志:"))
        main_layout.addWidget(self.log_text)
        
        self.log("程序启动完成")
        
    def create_connection_group(self):
        """创建连接设置组"""
        group = QGroupBox("PLC连接设置")
        layout = QGridLayout(group)
        
        # IP地址
        layout.addWidget(QLabel("IP地址:"), 0, 0)
        self.ip_edit = QLineEdit("*************")
        layout.addWidget(self.ip_edit, 0, 1)
        
        # Rack
        layout.addWidget(QLabel("Rack:"), 0, 2)
        self.rack_spin = QSpinBox()
        self.rack_spin.setRange(0, 7)
        self.rack_spin.setValue(0)
        layout.addWidget(self.rack_spin, 0, 3)
        
        # Slot
        layout.addWidget(QLabel("Slot:"), 0, 4)
        self.slot_spin = QSpinBox()
        self.slot_spin.setRange(0, 31)
        self.slot_spin.setValue(1)
        layout.addWidget(self.slot_spin, 0, 5)
        
        # 连接按钮
        self.connect_btn = QPushButton("连接")
        self.connect_btn.clicked.connect(self.toggle_connection)
        layout.addWidget(self.connect_btn, 0, 6)
        
        # 连接状态
        self.status_label = QLabel("未连接")
        self.status_label.setStyleSheet("color: red; font-weight: bold;")
        layout.addWidget(self.status_label, 0, 7)
        
        return group
        
    def create_data_tab(self):
        """创建数据读写标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 读取数据组
        read_group = QGroupBox("读取数据")
        read_layout = QGridLayout(read_group)
        
        read_layout.addWidget(QLabel("数据块:"), 0, 0)
        self.read_db_spin = QSpinBox()
        self.read_db_spin.setRange(1, 65535)
        self.read_db_spin.setValue(1)
        read_layout.addWidget(self.read_db_spin, 0, 1)
        
        read_layout.addWidget(QLabel("起始地址:"), 0, 2)
        self.read_start_spin = QSpinBox()
        self.read_start_spin.setRange(0, 65535)
        read_layout.addWidget(self.read_start_spin, 0, 3)
        
        read_layout.addWidget(QLabel("长度:"), 0, 4)
        self.read_size_spin = QSpinBox()
        self.read_size_spin.setRange(1, 1024)
        self.read_size_spin.setValue(10)
        read_layout.addWidget(self.read_size_spin, 0, 5)
        
        self.read_btn = QPushButton("读取")
        self.read_btn.clicked.connect(self.read_data)
        read_layout.addWidget(self.read_btn, 0, 6)
        
        self.read_result = QTextEdit()
        self.read_result.setMaximumHeight(100)
        read_layout.addWidget(self.read_result, 1, 0, 1, 7)
        
        layout.addWidget(read_group)
        
        # 写入数据组
        write_group = QGroupBox("写入数据")
        write_layout = QGridLayout(write_group)
        
        write_layout.addWidget(QLabel("数据块:"), 0, 0)
        self.write_db_spin = QSpinBox()
        self.write_db_spin.setRange(1, 65535)
        self.write_db_spin.setValue(1)
        write_layout.addWidget(self.write_db_spin, 0, 1)
        
        write_layout.addWidget(QLabel("起始地址:"), 0, 2)
        self.write_start_spin = QSpinBox()
        self.write_start_spin.setRange(0, 65535)
        write_layout.addWidget(self.write_start_spin, 0, 3)
        
        write_layout.addWidget(QLabel("数据类型:"), 0, 4)
        self.data_type_combo = QComboBox()
        self.data_type_combo.addItems(["BYTE", "WORD", "DWORD", "INT", "DINT", "REAL"])
        write_layout.addWidget(self.data_type_combo, 0, 5)
        
        write_layout.addWidget(QLabel("数值:"), 1, 0)
        self.write_value_edit = QLineEdit("0")
        write_layout.addWidget(self.write_value_edit, 1, 1, 1, 4)
        
        self.write_btn = QPushButton("写入")
        self.write_btn.clicked.connect(self.write_data)
        write_layout.addWidget(self.write_btn, 1, 5)
        
        layout.addWidget(write_group)
        
        return widget
        
    def create_monitor_tab(self):
        """创建监控标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 监控设置
        monitor_group = QGroupBox("监控设置")
        monitor_layout = QHBoxLayout(monitor_group)
        
        monitor_layout.addWidget(QLabel("数据块:"))
        self.monitor_db_spin = QSpinBox()
        self.monitor_db_spin.setRange(1, 65535)
        self.monitor_db_spin.setValue(1)
        monitor_layout.addWidget(self.monitor_db_spin)
        
        monitor_layout.addWidget(QLabel("起始地址:"))
        self.monitor_start_spin = QSpinBox()
        self.monitor_start_spin.setRange(0, 65535)
        monitor_layout.addWidget(self.monitor_start_spin)
        
        monitor_layout.addWidget(QLabel("长度:"))
        self.monitor_size_spin = QSpinBox()
        self.monitor_size_spin.setRange(1, 100)
        self.monitor_size_spin.setValue(20)
        monitor_layout.addWidget(self.monitor_size_spin)
        
        self.monitor_btn = QPushButton("开始监控")
        self.monitor_btn.clicked.connect(self.toggle_monitor)
        monitor_layout.addWidget(self.monitor_btn)
        
        layout.addWidget(monitor_group)
        
        # 监控数据表格
        self.monitor_table = QTableWidget()
        self.monitor_table.setColumnCount(4)
        self.monitor_table.setHorizontalHeaderLabels(["地址", "字节值", "字值", "双字值"])
        self.monitor_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.monitor_table)
        
        return widget
        
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        
    def toggle_connection(self):
        """切换连接状态"""
        if not self.connected:
            self.connect_to_plc()
        else:
            self.disconnect_from_plc()
            
    def connect_to_plc(self):
        """连接到PLC"""
        ip = self.ip_edit.text()
        rack = self.rack_spin.value()
        slot = self.slot_spin.value()
        
        self.log(f"正在连接到 {ip}, Rack:{rack}, Slot:{slot}...")
        
        # 使用线程进行连接
        self.connection_thread = S7ConnectionThread(ip, rack, slot)
        self.connection_thread.connection_result.connect(self.on_connection_result)
        self.connection_thread.start()
        
    def on_connection_result(self, success, message):
        """连接结果处理"""
        if success:
            try:
                self.client.connect(self.ip_edit.text(), self.rack_spin.value(), self.slot_spin.value())
                self.connected = True
                self.connect_btn.setText("断开")
                self.status_label.setText("已连接")
                self.status_label.setStyleSheet("color: green; font-weight: bold;")
                self.log(message)
            except Exception as e:
                self.log(f"连接失败: {str(e)}")
        else:
            self.log(message)
            
    def disconnect_from_plc(self):
        """断开PLC连接"""
        try:
            if self.monitor_timer.isActive():
                self.monitor_timer.stop()
                self.monitor_btn.setText("开始监控")
                
            self.client.disconnect()
            self.connected = False
            self.connect_btn.setText("连接")
            self.status_label.setText("未连接")
            self.status_label.setStyleSheet("color: red; font-weight: bold;")
            self.log("已断开连接")
        except Exception as e:
            self.log(f"断开连接错误: {str(e)}")

    def read_data(self):
        """读取PLC数据"""
        if not self.connected:
            QMessageBox.warning(self, "警告", "请先连接PLC")
            return

        try:
            db_num = self.read_db_spin.value()
            start = self.read_start_spin.value()
            size = self.read_size_spin.value()

            self.log(f"读取 DB{db_num}.{start}, 长度:{size}")

            # 读取数据
            data = self.client.db_read(db_num, start, size)

            # 显示原始字节数据
            hex_data = ' '.join([f'{b:02X}' for b in data])
            result_text = f"原始数据 (HEX): {hex_data}\n\n"

            # 解析不同数据类型
            result_text += "数据解析:\n"
            for i in range(0, len(data), 2):
                if i + 1 < len(data):
                    # WORD (16位无符号整数)
                    word_val = struct.unpack('>H', data[i:i+2])[0]
                    result_text += f"  地址 {start+i}: WORD = {word_val}\n"

                    # INT (16位有符号整数)
                    int_val = struct.unpack('>h', data[i:i+2])[0]
                    result_text += f"  地址 {start+i}: INT = {int_val}\n"

            # 如果数据长度足够，显示DWORD和REAL
            for i in range(0, len(data), 4):
                if i + 3 < len(data):
                    # DWORD (32位无符号整数)
                    dword_val = struct.unpack('>I', data[i:i+4])[0]
                    result_text += f"  地址 {start+i}: DWORD = {dword_val}\n"

                    # DINT (32位有符号整数)
                    dint_val = struct.unpack('>i', data[i:i+4])[0]
                    result_text += f"  地址 {start+i}: DINT = {dint_val}\n"

                    # REAL (32位浮点数)
                    real_val = struct.unpack('>f', data[i:i+4])[0]
                    result_text += f"  地址 {start+i}: REAL = {real_val:.3f}\n"

            self.read_result.setText(result_text)
            self.log("数据读取成功")

        except Exception as e:
            error_msg = f"读取数据失败: {str(e)}"
            self.log(error_msg)
            QMessageBox.critical(self, "错误", error_msg)

    def write_data(self):
        """写入PLC数据"""
        if not self.connected:
            QMessageBox.warning(self, "警告", "请先连接PLC")
            return

        try:
            db_num = self.write_db_spin.value()
            start = self.write_start_spin.value()
            data_type = self.data_type_combo.currentText()
            value_str = self.write_value_edit.text()

            # 根据数据类型转换数值
            if data_type == "BYTE":
                value = int(value_str)
                if not 0 <= value <= 255:
                    raise ValueError("BYTE值必须在0-255之间")
                data = struct.pack('B', value)

            elif data_type == "WORD":
                value = int(value_str)
                if not 0 <= value <= 65535:
                    raise ValueError("WORD值必须在0-65535之间")
                data = struct.pack('>H', value)

            elif data_type == "DWORD":
                value = int(value_str)
                if not 0 <= value <= 4294967295:
                    raise ValueError("DWORD值必须在0-4294967295之间")
                data = struct.pack('>I', value)

            elif data_type == "INT":
                value = int(value_str)
                if not -32768 <= value <= 32767:
                    raise ValueError("INT值必须在-32768到32767之间")
                data = struct.pack('>h', value)

            elif data_type == "DINT":
                value = int(value_str)
                if not -2147483648 <= value <= 2147483647:
                    raise ValueError("DINT值必须在-2147483648到2147483647之间")
                data = struct.pack('>i', value)

            elif data_type == "REAL":
                value = float(value_str)
                data = struct.pack('>f', value)

            self.log(f"写入 DB{db_num}.{start}, 类型:{data_type}, 值:{value_str}")

            # 写入数据
            self.client.db_write(db_num, start, data)
            self.log("数据写入成功")
            QMessageBox.information(self, "成功", "数据写入成功")

        except ValueError as e:
            error_msg = f"数值错误: {str(e)}"
            self.log(error_msg)
            QMessageBox.warning(self, "数值错误", error_msg)

        except Exception as e:
            error_msg = f"写入数据失败: {str(e)}"
            self.log(error_msg)
            QMessageBox.critical(self, "错误", error_msg)

    def toggle_monitor(self):
        """切换监控状态"""
        if not self.connected:
            QMessageBox.warning(self, "警告", "请先连接PLC")
            return

        if not self.monitor_timer.isActive():
            # 开始监控
            self.setup_monitor_table()
            self.monitor_timer.start(1000)  # 每秒更新一次
            self.monitor_btn.setText("停止监控")
            self.log("开始实时监控")
        else:
            # 停止监控
            self.monitor_timer.stop()
            self.monitor_btn.setText("开始监控")
            self.log("停止实时监控")

    def setup_monitor_table(self):
        """设置监控表格"""
        size = self.monitor_size_spin.value()
        self.monitor_table.setRowCount(size)

        start_addr = self.monitor_start_spin.value()
        for i in range(size):
            addr_item = QTableWidgetItem(f"DB{self.monitor_db_spin.value()}.{start_addr + i}")
            self.monitor_table.setItem(i, 0, addr_item)

    def update_monitor_data(self):
        """更新监控数据"""
        try:
            db_num = self.monitor_db_spin.value()
            start = self.monitor_start_spin.value()
            size = self.monitor_size_spin.value()

            # 读取数据
            data = self.client.db_read(db_num, start, size)

            # 更新表格
            for i in range(min(size, len(data))):
                # 字节值
                byte_val = data[i]
                byte_item = QTableWidgetItem(f"{byte_val}")
                self.monitor_table.setItem(i, 1, byte_item)

                # 字值 (需要两个字节)
                if i + 1 < len(data):
                    word_val = struct.unpack('>H', data[i:i+2])[0]
                    word_item = QTableWidgetItem(f"{word_val}")
                    self.monitor_table.setItem(i, 2, word_item)

                # 双字值 (需要四个字节)
                if i + 3 < len(data):
                    dword_val = struct.unpack('>I', data[i:i+4])[0]
                    dword_item = QTableWidgetItem(f"{dword_val}")
                    self.monitor_table.setItem(i, 3, dword_item)

        except Exception as e:
            self.log(f"监控数据更新失败: {str(e)}")
            self.monitor_timer.stop()
            self.monitor_btn.setText("开始监控")


def main():
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("S7 PLC 测试工具")
    app.setApplicationVersion("1.0")

    # 创建主窗口
    window = S7PLCTester()
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
