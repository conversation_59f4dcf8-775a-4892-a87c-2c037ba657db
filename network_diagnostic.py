#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
S7 PLC 网络诊断工具
"""

import socket
import subprocess
import sys
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

def ping_test(ip):
    """测试ping连通性"""
    print(f"\n=== PING 测试 {ip} ===")
    try:
        # Windows和Linux的ping命令参数不同
        if sys.platform.startswith('win'):
            result = subprocess.run(['ping', '-n', '4', ip], 
                                  capture_output=True, text=True, timeout=10)
        else:
            result = subprocess.run(['ping', '-c', '4', ip], 
                                  capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✓ PING 成功")
            return True
        else:
            print("✗ PING 失败")
            print(result.stdout)
            return False
    except Exception as e:
        print(f"✗ PING 测试异常: {e}")
        return False

def port_scan(ip, ports):
    """扫描指定端口"""
    print(f"\n=== 端口扫描 {ip} ===")
    open_ports = []
    
    def scan_port(port):
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((ip, port))
            sock.close()
            return port, result == 0
        except:
            return port, False
    
    # 使用线程池并发扫描
    with ThreadPoolExecutor(max_workers=10) as executor:
        future_to_port = {executor.submit(scan_port, port): port for port in ports}
        
        for future in as_completed(future_to_port):
            port, is_open = future.result()
            if is_open:
                print(f"✓ 端口 {port} 开放")
                open_ports.append(port)
            else:
                print(f"✗ 端口 {port} 关闭")
    
    return open_ports

def s7_connection_test(ip, rack=0, slot=1):
    """测试S7连接"""
    print(f"\n=== S7 连接测试 {ip} (Rack:{rack}, Slot:{slot}) ===")
    
    try:
        import snap7
        client = snap7.client.Client()
        
        print("正在连接...")
        client.connect(ip, rack, slot)
        
        if client.get_connected():
            print("✓ S7 连接成功")
            
            # 获取CPU信息
            try:
                cpu_info = client.get_cpu_info()
                print(f"CPU型号: {cpu_info.ModuleTypeName}")
                print(f"CPU版本: {cpu_info.ModuleVersion}")
                print(f"序列号: {cpu_info.SerialNumber}")
            except Exception as e:
                print(f"获取CPU信息失败: {e}")
            
            # 测试读取
            try:
                print("测试读取 DB1.0 (2字节)...")
                data = client.db_read(1, 0, 2)
                print(f"✓ 读取成功: {data.hex()}")
            except Exception as e:
                print(f"✗ 读取测试失败: {e}")
            
            client.disconnect()
            return True
        else:
            print("✗ S7 连接失败")
            return False
            
    except ImportError:
        print("✗ snap7 库未安装")
        return False
    except Exception as e:
        print(f"✗ S7 连接异常: {e}")
        return False

def network_info():
    """显示网络信息"""
    print("\n=== 本机网络信息 ===")
    try:
        # 获取本机IP
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        print(f"主机名: {hostname}")
        print(f"本机IP: {local_ip}")
        
        # 获取所有网络接口
        if sys.platform.startswith('win'):
            result = subprocess.run(['ipconfig'], capture_output=True, text=True)
            print("\n网络接口信息:")
            print(result.stdout)
        else:
            result = subprocess.run(['ifconfig'], capture_output=True, text=True)
            print("\n网络接口信息:")
            print(result.stdout)
            
    except Exception as e:
        print(f"获取网络信息失败: {e}")

def main():
    print("S7 PLC 网络诊断工具")
    print("=" * 50)
    
    # 显示网络信息
    network_info()
    
    # 获取用户输入
    ip = input("\n请输入PLC IP地址 (默认: *************): ").strip()
    if not ip:
        ip = "*************"
    
    rack_input = input("请输入Rack (默认: 0): ").strip()
    rack = int(rack_input) if rack_input else 0
    
    slot_input = input("请输入Slot (默认: 1): ").strip()
    slot = int(slot_input) if slot_input else 1
    
    print(f"\n开始诊断 PLC: {ip}")
    print("=" * 50)
    
    # 1. PING测试
    ping_ok = ping_test(ip)
    
    # 2. 端口扫描
    common_ports = [102, 80, 443, 21, 22, 23, 502, 503]  # 102是S7通信端口
    open_ports = port_scan(ip, common_ports)
    
    # 3. S7连接测试
    if 102 in open_ports:
        s7_ok = s7_connection_test(ip, rack, slot)
    else:
        print("\n✗ 端口102未开放，跳过S7连接测试")
        s7_ok = False
    
    # 总结
    print("\n" + "=" * 50)
    print("诊断总结:")
    print(f"PING连通性: {'✓ 正常' if ping_ok else '✗ 失败'}")
    print(f"端口102状态: {'✓ 开放' if 102 in open_ports else '✗ 关闭'}")
    print(f"S7连接: {'✓ 成功' if s7_ok else '✗ 失败'}")
    
    if not ping_ok:
        print("\n建议检查:")
        print("1. 网络连接是否正常")
        print("2. IP地址是否正确")
        print("3. 防火墙设置")
        print("4. PLC是否在线")
    
    elif 102 not in open_ports:
        print("\n建议检查:")
        print("1. PLC是否启用了以太网通信")
        print("2. PLC防火墙设置")
        print("3. 网络安全设置")
        print("4. PLC程序中的通信配置")
    
    elif not s7_ok:
        print("\n建议检查:")
        print("1. Rack和Slot参数是否正确")
        print("2. PLC是否允许外部连接")
        print("3. 连接数是否已满")
        print("4. PLC保护设置")

if __name__ == "__main__":
    main()
