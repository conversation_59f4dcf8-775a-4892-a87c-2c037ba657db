import { ref, computed, reactive, watch, nextTick, onMounted, onUnmounted, defineProps } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'

// 导入现有的composables
import { useWebSDK } from './useWebSDK'
import { useROIDrawer } from './useROIDrawer'
import { useMotionDetection } from './useMotionDetection'
import { useROIDetectionController } from './useROIDetectionController'
import { useOperationLog } from './useOperationLog'
import { useROILoader } from './useROILoader'
import { useROILoadHandler } from './useROILoadHandler'
import { useConfigSaver } from './useConfigSaver'

// 导入API函数
import {
  getDetectionGroup,
  getGlobalSettings,
  updateConfig,
  getAuthHeaders
} from '../api'

// 导入类型定义
import type { GlobalSettings } from '../types'

// 声明全局变量
declare global {
  interface Window {
    WebVideoCtrl: any;
  }
  const $: any;
}

export function useVideoPreviewMain() {
  const route = useRoute()
  
  // Props和计算属性
  const props = defineProps<{
    // 定义props类型
  }>()
  
  // URL参数解析
  const urlDetectionMode = computed(() => route.query.detection_mode as string)
  const urlTemplateId = computed(() => route.query.template_id as string)
  const urlDetectionGroupId = computed(() => route.query.detection_group_id as string)
  
  // 配置参数
  const configParams = ref({
    detectionMode: '',
    templateId: '',
    detectionGroupId: ''
  })
  
  const templateInfo = ref(null)
  const dieCasterInfo = ref(null)
  const detectionGroupInfo = ref(null)
  
  // 调试开关
  const DEBUG_ENABLED = ref(true)
  
  // 使用现有的composables
  const webSDK = useWebSDK()
  const roiDrawer = useROIDrawer()
  const motionDetection = useMotionDetection()
  const roiDetectionController = useROIDetectionController()
  const operationLog = useOperationLog()
  const roiLoader = useROILoader()
  const roiLoadHandler = useROILoadHandler()
  const configSaver = useConfigSaver()
  
  // WebSDK相关
  const {
    webVideoCtrl,
    videoSources,
    selectedVideoSource,
    isConnecting,
    isPreviewActive,
    videoContainerStyle,
    initWebSDK,
    refreshVideoSources,
    onVideoSourceChange,
    startPreview,
    stopPreview
  } = webSDK
  
  // ROI相关
  const {
    roiDrawerInstance,
    roiList,
    roiCount,
    selectedROIAttribute,
    isDrawingEnabled,
    highlightedROIId,
    onROIAttributeChange,
    toggleDrawMode,
    clearROIs,
    saveROIs,
    exportROIs,
    importROIs,
    loadROIListToVideo,
    testROILoad,
    manualInitROI,
    editROI,
    deleteROI,
    toggleROIHighlight,
    clearAttributeROIs,
    onROIDrawerReady
  } = roiDrawer
  
  // 检测相关
  const {
    isDetectionActive,
    roiDetectionResult,
    detectionStats,
    roiDetectors,
    roiDetectionStates,
    startDetection,
    stopDetection,
    setRoiDetector
  } = motionDetection
  
  // 全局设置
  const globalSettings = ref<GlobalSettings>({
    delayTime: 5,
    pauseThreshold: 15,
    cooldownTime: 3,
    cardDelayTime: 2
  })
  
  // 操作日志
  const {
    operationInfos,
    addOperationInfo,
    clearOperationInfo
  } = operationLog
  
  // 视频播放器
  const videoPlayer = ref(null)
  const currentVideoSource = ref(null)
  
  // 调试相关
  const parameterLogViewer = ref(null)
  const algorithmDebugLog = ref('')
  
  // URL参数解析函数
  const parseUrlParams = () => {
    configParams.value = {
      detectionMode: urlDetectionMode.value || '',
      templateId: urlTemplateId.value || '',
      detectionGroupId: urlDetectionGroupId.value || ''
    }
    
    addOperationInfo(`[配置] URL参数解析完成: 检测模式=${configParams.value.detectionMode}, 模板ID=${configParams.value.templateId}, 检测组ID=${configParams.value.detectionGroupId}`)
  }
  
  // 视频源配置加载
  const loadVideoSourceFromConfig = async () => {
    // 实现视频源配置加载逻辑
    addOperationInfo('[配置] 开始加载视频源配置')
  }
  
  // 视频尺寸变化处理
  const onVideoSizeChanged = (size: { width: number, height: number }) => {
    addOperationInfo(`[视频] 视频尺寸变化: ${size.width}x${size.height}`)
  }
  
  // ROI检测启动
  const startROIDetection = async () => {
    if (roiList.value.length === 0) {
      ElMessage.warning('请先绘制ROI区域')
      return
    }
    
    try {
      // 启动所有ROI的检测
      roiList.value.forEach(roi => {
        roiDetectionController.startROIDetection(roi.roi_id, roi.attribute || 'pailiao')
      })
      
      startDetection()
      addOperationInfo('[检测] ROI区域检测已启动')
      ElMessage.success('检测已启动')
    } catch (error) {
      addOperationInfo(`[检测] 启动检测失败: ${error}`)
      ElMessage.error('启动检测失败')
    }
  }
  
  // ROI检测停止
  const stopROIDetection = () => {
    // 停止所有ROI的检测状态
    roiList.value.forEach(roi => {
      roiDetectionController.stopROIDetection(roi.roi_id, '检测停止')
    })
    
    stopDetection()
    addOperationInfo('[检测] ROI区域检测已停止')
    ElMessage.success('检测已停止')
  }
  
  // 切换ROI检测
  const toggleROIDetection = () => {
    if (isDetectionActive.value) {
      stopROIDetection()
    } else {
      startROIDetection()
    }
  }
  
  // ROI算法变更
  const onRoiAlgorithmChange = ({ roiId, algorithm }: { roiId: string, algorithm: string }) => {
    // 更新ROI检测器配置
    if (!roiDetectors.value[roiId]) {
      roiDetectors.value[roiId] = { type: algorithm }
    } else {
      roiDetectors.value[roiId].type = algorithm
    }
    
    // 更新后端检测器
    setRoiDetector(roiId, algorithm as any)
    
    addOperationInfo(`[检测] ROI ${roiId} 算法变更为 ${algorithm === 'background_subtraction' ? '背景减除法' : '帧差法'}`)
  }
  
  // ROI算法配置
  const onRoiAlgorithmConfig = ({ roiId, params }: { roiId: string, params: any }) => {
    if (DEBUG_ENABLED.value) {
      console.log(`[算法配置] 保存ROI ${roiId} 的算法参数:`, params)
      console.log(`[算法配置] 保存前roiDetectors[${roiId}]:`, roiDetectors.value[roiId])
    }

    // 更新ROI检测器配置
    roiDetectors.value[roiId] = {
      ...roiDetectors.value[roiId],
      ...params
    }

    if (DEBUG_ENABLED.value) {
      console.log(`[算法配置] 保存后roiDetectors[${roiId}]:`, roiDetectors.value[roiId])
      console.log(`[算法配置] 当前所有roiDetectors:`, roiDetectors.value)
    }

    // 同时更新ROI对象中的params字段
    const roiIndex = roiList.value.findIndex(roi => roi.roi_id === roiId)
    let currentRoi = null
    if (roiIndex !== -1) {
      currentRoi = roiList.value[roiIndex]

      // 🔥 关键修复：保持ROI的attribute属性不变，只更新params
      const originalAttribute = currentRoi.attribute
      currentRoi.params = params

      // 确保attribute属性不被覆盖
      if (originalAttribute) {
        currentRoi.attribute = originalAttribute
        addOperationInfo(`[ROI] 保持ROI ${roiId} 的attribute属性: ${originalAttribute}`)
      }

      // 确保坐标信息完整（防止丢失）
      if (!currentRoi.coordinates && currentRoi.points) {
        currentRoi.coordinates = currentRoi.points
      } else if (!currentRoi.points && currentRoi.coordinates) {
        currentRoi.points = currentRoi.coordinates
      }

      addOperationInfo(`[ROI] 更新ROI对象 ${roiId} 的params字段: ${JSON.stringify(params)}`)
      addOperationInfo(`[ROI] - 当前坐标: ${(currentRoi.coordinates || []).length}个点`)
      addOperationInfo(`[ROI] - 当前attribute: ${currentRoi.attribute}`)
    }

    // 🔥 关键修复：根据ROI属性和算法类型确定后端检测器类型
    let backendDetectorType = params.type

    if (currentRoi && currentRoi.attribute === 'pailiao' && params.type === 'motion') {
      // 对于pailiao类型的ROI，需要根据运动检测算法确定具体的检测器类型
      const algorithm = params.运动检测?.algorithm
      if (algorithm === '背景减除法') {
        backendDetectorType = 'background_subtraction'
      } else if (algorithm === '帧差法') {
        backendDetectorType = 'frame_difference'
      }
      addOperationInfo(`[算法配置] pailiao ROI ${roiId}: 算法=${algorithm} → 后端类型=${backendDetectorType}`)
    }

    // 更新后端检测器
    setRoiDetector(roiId, backendDetectorType, params)

    // 保存到数据库
    saveROIConfigToDatabase(roiId)

    addOperationInfo(`[检测] 更新ROI ${roiId} 算法配置`)
  }
  
  // 保存ROI配置到数据库
  const saveROIConfigToDatabase = async (roiId: string) => {
    // 实现保存逻辑
    addOperationInfo(`[数据库] 保存ROI ${roiId} 配置到数据库`)
  }

  // 处理全局设置应用
  const onGlobalSettingsApply = (settings: GlobalSettings) => {
    addOperationInfo(`[设置] 手动应用全局设置: 延时=${settings.delayTime}秒, 暂停阈值=${settings.pauseThreshold}秒, 冷却时间=${settings.cooldownTime}秒`)

    // 立即发送设置到后端
    updateConfig({
      global_settings: {
        delay_time: settings.delayTime,
        pause_threshold: settings.pauseThreshold,
        cooldown_time: settings.cooldownTime
      }
    })
  }

  // 监听全局设置变化（避免重复发送）
  let lastGlobalSettingsUpdate = 0
  watch(globalSettings, (newSettings: GlobalSettings) => {
    const now = Date.now()
    // 防抖：如果距离上次更新不到1秒，则跳过
    if (now - lastGlobalSettingsUpdate < 1000) {
      addOperationInfo(`[设置] 跳过重复的全局设置更新`)
      return
    }

    lastGlobalSettingsUpdate = now
    addOperationInfo(`[设置] 监听到全局设置变化: 延时=${newSettings.delayTime}秒, 暂停阈值=${newSettings.pauseThreshold}秒, 冷却时间=${newSettings.cooldownTime}秒, 卡料延时=${newSettings.cardDelayTime}秒`)

    // 实时发送全局设置到后端（无论是否正在检测）
    updateConfig({
      global_settings: {
        delay_time: newSettings.delayTime,
        pause_threshold: newSettings.pauseThreshold,
        cooldown_time: newSettings.cooldownTime,
        card_delay_time: newSettings.cardDelayTime
      }
    })
  }, { deep: true })

  // 从检测组config_json自动加载ROI
  const loadROIFromDetectionGroupConfig = async () => {
    if (!configParams.value.detectionGroupId) {
      addOperationInfo('[AUTO-ROI] 缺少检测组ID，跳过自动ROI加载')
      return
    }

    try {
      addOperationInfo(`[AUTO-ROI] 正在获取检测组 ${configParams.value.detectionGroupId} 的配置信息...`)

      // 获取检测组详细信息
      let detectionGroup
      try {
        detectionGroup = await getDetectionGroup(Number(configParams.value.detectionGroupId))
        addOperationInfo(`[AUTO-ROI] 获取到检测组信息: ${detectionGroup.name || detectionGroup.id}`)
      } catch (error) {
        addOperationInfo(`[AUTO-ROI] ❌ 获取检测组信息失败: ${error}`)
        return
      }

      // 检查是否有config_json字段
      if (!detectionGroup.config_json) {
        addOperationInfo('[AUTO-ROI] 检测组中没有config_json配置，跳过自动ROI加载')
        return
      }

      addOperationInfo(`[AUTO-ROI] 解析config_json: ${JSON.stringify(detectionGroup.config_json)}`)

      // 解析config_json中的roiIds
      const configJson = detectionGroup.config_json
      if (!configJson.roiIds || !Array.isArray(configJson.roiIds)) {
        addOperationInfo('[AUTO-ROI] config_json中没有roiIds字段或格式不正确，跳过自动ROI加载')
        return
      }

      const roiIds = configJson.roiIds
      addOperationInfo(`[AUTO-ROI] 发现 ${roiIds.length} 个ROI ID: ${roiIds.join(', ')}`)

      // 如果有全局设置，也一并应用
      if (configJson.globalSettings) {
        addOperationInfo(`[AUTO-ROI] 应用全局设置: ${JSON.stringify(configJson.globalSettings)}`)
        globalSettings.value = {
          delayTime: configJson.globalSettings.delayTime || globalSettings.value.delayTime,
          pauseThreshold: configJson.globalSettings.pauseThreshold || globalSettings.value.pauseThreshold,
          cooldownTime: configJson.globalSettings.cooldownTime || globalSettings.value.cooldownTime,
          cardDelayTime: configJson.globalSettings.cardDelayTime || globalSettings.value.cardDelayTime
        }
      }

      // 等待ROI绘制器初始化完成
      let retryCount = 0
      const maxRetries = 10
      while (!roiDrawerInstance.value && retryCount < maxRetries) {
        addOperationInfo(`[AUTO-ROI] 等待ROI绘制器初始化... (${retryCount + 1}/${maxRetries})`)
        await new Promise(resolve => setTimeout(resolve, 500))
        retryCount++
      }

      if (!roiDrawerInstance.value) {
        addOperationInfo('[AUTO-ROI] ❌ ROI绘制器初始化超时，无法自动加载ROI')
        return
      }

      // 通过ROI ID获取ROI配置
      const roiConfigs = []
      for (const roiId of roiIds) {
        try {
          addOperationInfo(`[AUTO-ROI] 正在获取ROI ${roiId} 的配置...`)

          const roiResponse = await fetch(`/api/roi-config/${roiId}`, {
            headers: getAuthHeaders()
          })

          if (roiResponse.ok) {
            const roiResponseData = await roiResponse.json()
            // 🔥 修复：提取data字段，API返回格式为 {message: '...', data: {...}}
            const roiConfig = roiResponseData.data || roiResponseData
            roiConfigs.push(roiConfig)
            addOperationInfo(`[AUTO-ROI] ✅ 获取ROI ${roiId} 配置成功: ${roiConfig.name || roiConfig.roi_id}`)
          } else {
            addOperationInfo(`[AUTO-ROI] ❌ 获取ROI ${roiId} 配置失败: HTTP ${roiResponse.status}`)
          }
        } catch (error) {
          addOperationInfo(`[AUTO-ROI] ❌ 获取ROI ${roiId} 配置异常: ${error}`)
        }
      }

      if (roiConfigs.length === 0) {
        addOperationInfo('[AUTO-ROI] ❌ 没有成功获取到任何ROI配置')
        return
      }

      addOperationInfo(`[AUTO-ROI] 成功获取 ${roiConfigs.length} 个ROI配置，开始自动加载...`)

      // 使用现有的ROI加载逻辑
      const success = await roiLoadHandler.loadROIsToDrawer(
        roiConfigs,
        roiDrawerInstance.value,
        roiList.value,
        roiDetectors.value,
        setRoiDetector,
        addOperationInfo
      )

      if (success) {
        addOperationInfo(`[AUTO-ROI] ✅ 自动加载 ${roiConfigs.length} 个ROI成功！`)
        ElMessage.success(`自动加载 ${roiConfigs.length} 个ROI成功！`)

        // 🔥 新增：ROI加载成功后自动启动卡料检测
        if (roiList.value.length > 0 && isPreviewActive.value) {
          addOperationInfo(`[AUTO-DETECTION] ROI加载成功，准备自动启动卡料检测...`)

          // 等待一小段时间确保ROI完全加载和绘制完成
          await nextTick()
          setTimeout(async () => {
            try {
              await startROIDetection()
              addOperationInfo(`[AUTO-DETECTION] ✅ 卡料检测已自动启动`)
              ElMessage.success('卡料检测已自动启动')
            } catch (error) {
              addOperationInfo(`[AUTO-DETECTION] ❌ 自动启动卡料检测失败: ${error}`)
              ElMessage.warning('ROI加载成功，但自动启动检测失败，请手动启动')
            }
          }, 1000) // 延迟1秒确保ROI绘制和参数配置完成
        } else {
          if (!isPreviewActive.value) {
            addOperationInfo(`[AUTO-DETECTION] 视频预览未激活，无法自动启动检测`)
            ElMessage.info('ROI加载成功，请先启动视频预览后手动开始检测')
          } else {
            addOperationInfo(`[AUTO-DETECTION] 没有可用的ROI，无法自动启动检测`)
          }
        }
      } else {
        addOperationInfo('[AUTO-ROI] ❌ 自动加载ROI失败')
        ElMessage.error('自动加载ROI失败')
      }

    } catch (error) {
      addOperationInfo(`[AUTO-ROI] ❌ 自动加载ROI异常: ${error}`)
      ElMessage.error('自动加载ROI异常')
    }
  }

  // 处理ROI加载
  const handleLoadROIs = async (selectedROIs: any[]) => {
    addOperationInfo(`[ROI-LOAD] 开始加载 ${selectedROIs.length} 个ROI`)

    try {
      const success = await roiLoadHandler.loadROIsToDrawer(
        selectedROIs,
        roiDrawerInstance.value,
        roiList.value,
        roiDetectors.value,
        setRoiDetector,
        addOperationInfo
      )

      if (success) {
        addOperationInfo(`[ROI-LOAD] ✅ 成功加载 ${selectedROIs.length} 个ROI`)
        // 关闭对话框
        roiLoader.hideLoadDialog()
      }
    } catch (error) {
      addOperationInfo(`[ROI-LOAD] ❌ ROI加载失败: ${error}`)
    }
  }

  // 强制初始化ROI
  const forceInitROI = () => {
    addOperationInfo('[DEBUG] 强制初始化ROI绘制器')
    // 实现强制初始化逻辑
  }

  // 检查ROI状态
  const checkROIStatus = () => {
    addOperationInfo('[DEBUG] 检查ROI状态')
    // 实现状态检查逻辑
  }

  // 组件生命周期钩子
  onMounted(async () => {
    try {
      addOperationInfo('[系统] 视频预览组件开始初始化')

      // 解析URL参数
      parseUrlParams()

      // 设置默认视频源（用于ROI配置加载）
      currentVideoSource.value = {
        id: 'default_video_source',
        path: '/default/video/path'
      }

      // 检查WebSDK脚本是否已加载
      if (!(window as any).WebVideoCtrl) {
        addOperationInfo('[ERROR] WebSDK脚本未加载，请检查index.html中的脚本引用')
        ElMessage.error('WebSDK脚本未加载，请检查配置')
        return
      }

      // 检查jQuery是否已加载
      if (typeof $ === 'undefined') {
        addOperationInfo('[WARNING] jQuery未加载，将使用原生DOM解析XML')
      } else {
        addOperationInfo('[INFO] jQuery已加载，将使用jQuery解析XML')
      }

      // 初始化WebSDK和视频源
      await initWebSDK()
      await refreshVideoSources()

      // 根据URL参数加载视频源配置
      await loadVideoSourceFromConfig()

      // 初始化全局设置
      addOperationInfo('[设置] 初始化全局检测参数')
      // 从后端获取全局设置（如果有的话）
      try {
        const settings = await getGlobalSettings()
        if (settings && settings.global_settings) {
          globalSettings.value = {
            delayTime: settings.global_settings.delay_time || 5,
            pauseThreshold: settings.global_settings.pause_threshold || 15,
            cooldownTime: settings.global_settings.cooldown_time || 3,
            cardDelayTime: settings.global_settings.card_delay_time || 2
          }
          addOperationInfo('[设置] 已从服务器加载全局设置')
        }
      } catch (error) {
        if (DEBUG_ENABLED.value) {
          console.error('获取全局设置失败:', error)
        }
        addOperationInfo('[设置] 使用默认全局设置')
      }

      // 确保在DOM渲染完成后初始化ROI绘制器
      nextTick(() => {
        setTimeout(async () => {
          addOperationInfo('[INFO] 初始组件挂载完成，预检查ROI画布状态')
          if (videoPlayer.value?.roiDisplayCanvas) {
            const canvas = videoPlayer.value.roiDisplayCanvas
            addOperationInfo(`[INFO] 找到ROI画布: ${canvas.width}x${canvas.height}`)
          } else {
            addOperationInfo('[WARNING] 组件挂载后未找到ROI画布')
          }

          // 如果有检测组ID，尝试加载已有的ROI配置
          if (configParams.value.detectionGroupId) {
            addOperationInfo(`[配置] 检测到检测组ID: ${configParams.value.detectionGroupId}，尝试加载已有ROI配置`)
            // 自动解析检测组config_json并加载ROI
            await loadROIFromDetectionGroupConfig()
          } else {
            addOperationInfo('[INFO] ROI需要手动加载，请使用"加载已保存ROI"按钮')
          }
        }, 1000)
      })
    } catch (error) {
      if (DEBUG_ENABLED.value) {
        console.error('初始化失败:', error)
      }
      addOperationInfo(`[ERROR] 初始化失败: ${error instanceof Error ? error.message : String(error)}`)
      ElMessage.error('初始化失败，请检查WebSDK配置')
    }
  })

  // 组件卸载
  onUnmounted(() => {
    // 清理资源
    if (isDetectionActive.value) {
      stopROIDetection()
    }
    if (isPreviewActive.value) {
      stopPreview()
    }
    addOperationInfo('[系统] 视频预览组件卸载')
  })

  // 初始化函数
  const initializeComponent = async () => {
    try {
      addOperationInfo('[系统] 视频预览组件开始初始化')

      // 解析URL参数
      parseUrlParams()

      // 设置默认视频源（用于ROI配置加载）
      currentVideoSource.value = {
        id: 'default_video_source',
        path: '/default/video/path'
      }

      // 检查WebSDK脚本是否已加载
      if (!window.WebVideoCtrl) {
        addOperationInfo('[ERROR] WebSDK脚本未加载，请检查index.html中的脚本引用')
        ElMessage.error('WebSDK脚本未加载，请检查配置')
        return
      }

      // 检查jQuery是否已加载
      if (typeof $ === 'undefined') {
        addOperationInfo('[WARNING] jQuery未加载，将使用原生DOM解析XML')
      } else {
        addOperationInfo('[INFO] jQuery已加载，将使用jQuery解析XML')
      }

      // 初始化WebSDK和视频源
      await initWebSDK()
      await refreshVideoSources()

      // 根据URL参数加载视频源配置
      await loadVideoSourceFromConfig()

      // 初始化全局设置
      addOperationInfo('[设置] 初始化全局检测参数')
      // 从后端获取全局设置（如果有的话）
      try {
        const settings = await getGlobalSettings()
        if (settings && settings.global_settings) {
          globalSettings.value = {
            delayTime: settings.global_settings.delay_time || 5,
            pauseThreshold: settings.global_settings.pause_threshold || 15,
            cooldownTime: settings.global_settings.cooldown_time || 3,
            cardDelayTime: settings.global_settings.card_delay_time || 2
          }
          addOperationInfo('[设置] 已从服务器加载全局设置')
        }
      } catch (error) {
        if (DEBUG_ENABLED.value) {
          console.error('获取全局设置失败:', error)
        }
        addOperationInfo('[设置] 使用默认全局设置')
      }

      // 确保在DOM渲染完成后初始化ROI绘制器
      nextTick(() => {
        setTimeout(async () => {
          addOperationInfo('[INFO] 初始组件挂载完成，预检查ROI画布状态')
          if (videoPlayer.value?.roiDisplayCanvas) {
            const canvas = videoPlayer.value.roiDisplayCanvas
            addOperationInfo(`[INFO] 找到ROI画布: ${canvas.width}x${canvas.height}`)
          } else {
            addOperationInfo('[WARNING] 组件挂载后未找到ROI画布')
          }

          // 如果有检测组ID，尝试加载已有的ROI配置
          if (configParams.value.detectionGroupId) {
            addOperationInfo(`[配置] 检测到检测组ID: ${configParams.value.detectionGroupId}，尝试加载已有ROI配置`)
            // 自动解析检测组config_json并加载ROI
            await loadROIFromDetectionGroupConfig()
          } else {
            addOperationInfo('[INFO] ROI需要手动加载，请使用"加载已保存ROI"按钮')
          }
        }, 1000)
      })
    } catch (error) {
      if (DEBUG_ENABLED.value) {
        console.error('初始化失败:', error)
      }
      addOperationInfo(`[ERROR] 初始化失败: ${error instanceof Error ? error.message : String(error)}`)
      ElMessage.error('初始化失败，请检查WebSDK配置')
    }
  }

  // 在组件挂载时调用初始化
  onMounted(initializeComponent)
  
  return {
    // Props和computed
    props,
    urlDetectionMode,
    urlTemplateId,
    urlDetectionGroupId,
    
    // Configuration
    configParams,
    templateInfo,
    dieCasterInfo,
    detectionGroupInfo,
    
    // Debug
    DEBUG_ENABLED,
    
    // WebSDK
    webVideoCtrl,
    videoSources,
    selectedVideoSource,
    isConnecting,
    isPreviewActive,
    videoContainerStyle,
    refreshVideoSources,
    onVideoSourceChange,
    startPreview,
    stopPreview,
    
    // ROI
    roiDrawerInstance,
    roiList,
    roiCount,
    selectedROIAttribute,
    isDrawingEnabled,
    highlightedROIId,
    onROIAttributeChange,
    toggleDrawMode,
    clearROIs,
    saveROIs,
    exportROIs,
    importROIs,
    loadROIListToVideo,
    testROILoad,
    manualInitROI,
    editROI,
    deleteROI,
    toggleROIHighlight,
    clearAttributeROIs,
    onROIDrawerReady,
    
    // Detection
    isDetectionActive,
    roiDetectionResult,
    detectionStats,
    roiDetectors,
    roiDetectionStates,
    startROIDetection,
    stopROIDetection,
    toggleROIDetection,
    onRoiAlgorithmChange,
    onRoiAlgorithmConfig,
    
    // Global settings
    globalSettings,
    onGlobalSettingsApply,

    // Logging
    operationInfos,
    clearOperationInfo,

    // ROI loading
    roiLoader,
    handleLoadROIs,

    // Config saving
    configSaver,
    handleSaveConfig: configSaver.handleSaveConfig,

    // Video
    videoPlayer,
    currentVideoSource,
    onVideoSizeChanged,

    // Debug
    parameterLogViewer,
    algorithmDebugLog,
    forceInitROI,
    checkROIStatus,

    // Lifecycle functions
    parseUrlParams,
    loadVideoSourceFromConfig,
    loadROIFromDetectionGroupConfig,
    initializeComponent
  }
}
