#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
S7 PLC测试工具启动脚本
"""

import sys
import subprocess
import time

def check_dependencies():
    """检查依赖库"""
    print("检查依赖库...")
    
    missing_deps = []
    
    try:
        import PySide6
        print("✓ PySide6 已安装")
    except ImportError:
        missing_deps.append("PySide6")
        
    try:
        import snap7
        print("✓ snap7 已安装")
    except ImportError:
        missing_deps.append("python-snap7")
        
    if missing_deps:
        print(f"\n缺少依赖库: {', '.join(missing_deps)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_deps)}")
        return False
        
    print("✓ 所有依赖库已安装")
    return True

def start_simulator():
    """启动模拟器"""
    print("\n启动PLC模拟器...")
    try:
        from plc_simulator import S7PLCSimulator
        import threading
        
        simulator = S7PLCSimulator()
        
        # 在后台线程启动模拟器
        def run_sim():
            simulator.start()
            
        sim_thread = threading.Thread(target=run_sim)
        sim_thread.daemon = True
        sim_thread.start()
        
        time.sleep(2)  # 等待模拟器启动
        print("✓ PLC模拟器已启动 (127.0.0.1:102)")
        return simulator
        
    except Exception as e:
        print(f"✗ 启动模拟器失败: {e}")
        return None

def start_gui():
    """启动GUI程序"""
    print("\n启动测试工具...")
    try:
        from s7_plc_tester import main
        main()
    except Exception as e:
        print(f"✗ 启动GUI失败: {e}")

def main():
    print("S7 PLC 测试工具启动器")
    print("=" * 40)
    
    # 检查依赖
    if not check_dependencies():
        input("\n按回车键退出...")
        return
        
    print("\n选择启动模式:")
    print("1. 仅启动测试工具 (需要真实PLC)")
    print("2. 启动模拟器 + 测试工具 (推荐)")
    print("3. 仅启动模拟器")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        start_gui()
        
    elif choice == "2":
        simulator = start_simulator()
        if simulator:
            print("\n模拟器已启动，现在启动测试工具...")
            print("在测试工具中:")
            print("1. IP地址设为: 127.0.0.1")
            print("2. Rack: 0, Slot: 1")
            print("3. 点击连接按钮")
            input("\n按回车键启动测试工具...")
            start_gui()
        else:
            print("模拟器启动失败，无法继续")
            
    elif choice == "3":
        simulator = start_simulator()
        if simulator:
            print("\n模拟器已启动，按Ctrl+C停止")
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n停止模拟器...")
                simulator.stop()
        else:
            print("模拟器启动失败")
            
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
